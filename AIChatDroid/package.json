{"name": "ai.droid", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@vue/web-component-wrapper": "^1.3.0", "deep-chat": "^2.2.2", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1", "vue-web-component-wrapper": "^1.7.7"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.5", "@unocss/preset-attributify": "^66.5.1", "@unocss/preset-icons": "^66.5.1", "@unocss/preset-typography": "^66.5.1", "@unocss/preset-uno": "^66.5.1", "@unocss/preset-web-fonts": "^66.5.1", "@vitejs/plugin-vue": "^5.2.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "prettier": "3.6.2", "sass": "^1.93.1", "typescript": "~5.8.0", "unocss": "^66.5.1", "vite": "^6.0.0", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.4"}}