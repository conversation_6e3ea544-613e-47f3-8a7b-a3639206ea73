/*
 * @Author: tibin.zhang <EMAIL>
 * @Date: 2025-09-18 14:35:30
 * @LastEditors: tibin.zhang <EMAIL>
 * @LastEditTime: 2025-09-21 16:43:54
 * @FilePath: /AIChatDroid/vite.config.ts
 * @Description:
 *
 * Copyright © 2025 by PnR Data Service Co.,Ltd, All Rights Reserved.
 */
import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'

export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          // Treat deep-chat as a custom element
          isCustomElement: (tag) => tag === 'deep-chat'
        }
      }
    }),
    UnoCSS()
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:5001/',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
})
