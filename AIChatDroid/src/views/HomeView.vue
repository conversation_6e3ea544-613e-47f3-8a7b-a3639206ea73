<template>
  <div class="home-view">
    <!-- <h1>DG Chat</h1> -->
    <!-- demo/textInput are examples of passing an object directly into a property (if this does not work refer to data values like in the history example) -->
    <!-- history is an example of passing a data object into a property -->
    <deep-chat
      style="border-radius: 10px; border: unset; width: 100vw; height: 100vh;"
      :messageStyles="messageStyles"
      :textInput="textInput"
      :camera="true"
      :microphone="true"
      :submitButtonStyles="submitButtonStyles"
      auxiliaryStyle="
        ::-webkit-scrollbar {
          width: 10px;
          height: 10px;
        }
        ::-webkit-scrollbar-thumb {
          background-color: #54a7ff;
          border-radius: 5px;
        }
        ::-webkit-scrollbar-track {
          background-color: unset;
        }"
      :demo="true"
      ref="chatElementRef"
      :history="history"
    />

    <!-- Dynamic Component Renderer Container -->
    <!-- <div v-if="showComponent" class="component-overlay" @click.self="closeComponent">
      <div class="component-container">
        <div class="component-header">
          <span class="component-title">{{ componentData.message }}</span>
          <button @click="closeComponent" class="close-btn">×</button>
        </div>
        <DynamicComponentRenderer
          :message="componentData.message"
          :component-type="componentData.component_type"
          :component-data="componentData.component_data"
          @component-action="handleComponentAction"
        />
      </div>
    </div> -->
    <!-- <trans-trend-card :trans-list="transList" begin-date="20250401" end-date="20250407" /> -->
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, createApp, h } from 'vue'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import 'deep-chat'
import TransTrendCard from '../components/TransTrendCard.vue'
import DynamicComponentRenderer from '../components/DynamicComponentRenderer.vue'
import HelloWorld from '@/components/HelloWorld.vue'
import { defineCustomElement } from 'vue'

const chatElementRef = ref(null)

export const DynamicComponentRendererElement = defineCustomElement({
  ...DynamicComponentRenderer,
  styles: [DynamicComponentRenderer.styles],
})
customElements.define('dynamic-component-renderer', DynamicComponentRendererElement)

export const HelloWorldElement = defineCustomElement(HelloWorld)
customElements.define('hello-world', HelloWorldElement)

export default {
  components: { TransTrendCard, DynamicComponentRenderer },
  name: 'HomeView',
  setup() {
    const history = ref([
      { role: 'ai', text: '我是 DG!' },
    ])
    const messageStyles = ref({
      default: {
        user: {
          bubble: {
            background: 'linear-gradient(90deg, rgb(225 37 255) 0%, rgb(161, 99, 233) 100%)',
            color: 'white',
          },
        },
        loading: {
          bubble: {
            background: 'linear-gradient(90deg, rgb(0, 162, 255) 30%, rgb(197 119 255) 100%)',
            color: 'white',
          },
        },
        ai: {
          bubble: {
            background: 'linear-gradient(90deg, rgb(0, 162, 255) 30%, rgb(197 119 255) 100%)',
            color: 'white',
          },
        },
      },
    })

    

    const transList = ref([{
      "transDate": "20250401",
      "transAmt": "-130.50"
    }, {
      "transDate": "20250402",
      "transAmt": "2345.78"
    }, {
      "transDate": "20250403",
      "transAmt": "987.60"
    }, {
      "transDate": "20250404",
      "transAmt": "452.78"
    }, {
      "transDate": "20250405",
      "transAmt": "1326.80"
    }, {
      "transDate": "20250406",
      "transAmt": "245.78"
    }, {
      "transDate": "20250407",
      "transAmt": "0.0"
    }])

    const textInput = ref({
      placeholder: { text: 'Welcome to the DG!' },
      styles: {
        container: {
          backgroundColor: 'rgb(239 245 255)',
          color: '#0d008d',
        },
      },
    })

    const submitButtonStyles = ref({
      submit: {
        svg: {
          styles: {
            default: {
              filter:
                'brightness(0) saturate(100%) invert(26%) sepia(95%) saturate(6989%) hue-rotate(288deg) brightness(107%) contrast(122%)',
            },
          },
        },
      },
    })

    // AbortController for stopping streams
    let abortController = null
    let isRequestInProgress = false // Flag to prevent concurrent requests
    let retryCount = 0
    const maxRetries = 3
    const retryDelay = 2000 // 2 seconds
    let reconnectAttempts = 0
    const maxReconnectAttempts = 5
    const reconnectDelay = 3000 // 3 seconds
    let reconnectTimer = null

    // Auto-reconnect function for network failures
    const attemptReconnection = (lastMessage = null) => {
      if (reconnectAttempts >= maxReconnectAttempts) {
        console.log('Max reconnection attempts reached, stopping all reconnection attempts')

        // Reset all connection state
        reconnectAttempts = 0
        retryCount = 0
        isRequestInProgress = false

        // Clear any pending timers
        if (reconnectTimer) {
          clearTimeout(reconnectTimer)
          reconnectTimer = null
        }

        // Abort any ongoing connections
        if (abortController) {
          abortController.abort()
          abortController = null
        }

        // Show final error message to user
        if (
          chatElementRef.value &&
          chatElementRef.value.connect &&
          chatElementRef.value.connect.signals
        ) {
          chatElementRef.value.connect.signals.onResponse({
            error: '连接失败',
            text: '服务器连接失败，已停止重连。请检查网络连接和服务器状态后刷新页面重试。',
          })
          chatElementRef.value.connect.signals.onClose()
        }

        return // Stop all reconnection attempts
      }

      reconnectAttempts++
      console.log(`Attempting reconnection ${reconnectAttempts}/${maxReconnectAttempts}`)

      // Clear any existing timer
      if (reconnectTimer) {
        clearTimeout(reconnectTimer)
      }

      reconnectTimer = setTimeout(() => {
        // Reset connection state for this attempt
        retryCount = 0
        isRequestInProgress = false

        if (abortController) {
          abortController.abort()
          abortController = null
        }

        // Try to re-establish connection
        setupDeepChatConnection()

        // If we had a last message, try to resend it
        if (lastMessage && chatElementRef.value) {
          setTimeout(() => {
            if (chatElementRef.value && chatElementRef.value.connect) {
              console.log('Resending last message after reconnection')
              chatElementRef.value.connect.handler(
                lastMessage,
                chatElementRef.value.connect.signals,
              )
            }
          }, 1000)
        }
      }, reconnectDelay * reconnectAttempts) // Exponential backoff
    }

    const setupDeepChatConnection = () => {
      if (!chatElementRef.value) {
        console.warn('Chat element ref not available')
        return
      }

      chatElementRef.value.connect = {
        stream: true,
        handler: (body, signals) => {
          try {
            // Store the current request body for potential reconnection
            window.lastRequestBody = body

            // Prevent concurrent requests
            if (isRequestInProgress) {
              console.warn('Request already in progress, ignoring new request')
              signals.onResponse({
                error: '请求处理中',
                text: '请等待当前请求完成后再发送新消息。',
              })
              return
            }

            // Set request in progress flag
            isRequestInProgress = true

            // Create new AbortController for this request
            abortController = new AbortController()

            // Use proxy endpoint to avoid CORS issues
            const apiUrl = 'http://127.0.0.1:5001/chat/sse'

            // fetchEventSource(apiUrl, {
            //   method: 'POST',
            //   headers: {
            //     'Content-Type': 'application/json',
            //   },
            //   body: JSON.stringify({
            //     user_id: 111,
            //     message: body.messages?.[body.messages.length - 1]?.text || 'default message',
            //   }),
            //   signal: abortController.signal,
            //   openWhenHidden: false, // Don't retry when page is hidden
            //   retry: async (err) => {
            //     // Smart retry logic with network status check
            //     console.log(
            //       'Retry function called with error:',
            //       err.message,
            //       'Current retry count:',
            //       retryCount,
            //     )

            //     // Check if we've reached max retries
            //     if (retryCount >= maxRetries) {
            //       console.log('Max retries reached, terminating connection')
            //       retryCount = 0 // Reset for next request

            //       // Don't call signals here as it interferes with fetchEventSource
            //       // Let the onerror handler deal with the final error message
            //       return false // Terminate retry attempts
            //     }

            //     // Only retry on network-related errors
            //     if (err instanceof TypeError && err.message.includes('Failed to fetch')) {
            //       retryCount++
            //       console.log(
            //         `Scheduling retry attempt ${retryCount}/${maxRetries} after network error`,
            //       )

            //       // Return retry delay with exponential backoff
            //       return retryDelay * retryCount
            //     }

            //     // For non-network errors, don't retry
            //     console.log('Non-network error, not retrying:', err.message)
            //     retryCount = 0
            //     return false
            //   },
            //   async onopen(response) {
            //     if (response.ok) {
            //       retryCount = 0 // Reset retry count on successful connection
            //       reconnectAttempts = 0 // Reset reconnection attempts on successful connection

            //       // Clear any pending reconnection timer
            //       if (reconnectTimer) {
            //         clearTimeout(reconnectTimer)
            //         reconnectTimer = null
            //       }

            //       signals.onOpen() // stops the loading bubble
            //     } else {
            //       console.error('Connection failed:', response.status, response.statusText)

            //       // Abort the current request to prevent retries
            //       if (abortController) {
            //         abortController.abort()
            //         abortController = null
            //       }

            //       // Provide specific error messages based on status code
            //       let errorMessage = '服务器连接失败'
            //       if (response.status >= 500) {
            //         errorMessage = '服务器内部错误，请稍后重试'
            //       } else if (response.status === 404) {
            //         errorMessage = '请求的服务不存在'
            //       } else if (response.status === 403) {
            //         errorMessage = '访问被拒绝，请检查权限'
            //       } else if (response.status >= 400) {
            //         errorMessage = '请求参数错误'
            //       }

            //       signals.onResponse({
            //         error: `${errorMessage} (${response.status})`,
            //         text: `抱歉，${errorMessage}。请稍后重试。`,
            //       })

            //       // Ensure the connection is properly closed
            //       signals.onClose()
            //       isRequestInProgress = false // Reset request flag
            //     }
            //   },
            //   onmessage(event) {
            //     try {
            //       // Parse the SSE message data
            //       const data = event.data
            //       if (data && data !== '[DONE]') {
            //         // Try to parse JSON data structure
            //         try {
            //           const parsedData = JSON.parse(data)

            //           // Handle structured data with done and token fields
            //           if (typeof parsedData === 'object' && parsedData !== null) {
            //             if (parsedData.done === true) {
            //               // Terminate data stream when done=true
            //               console.log('Stream completed, terminating connection')
            //               signals.onClose()
            //               isRequestInProgress = false
            //               return
            //             } else if (parsedData.done === false && parsedData.token) {
            //               // Check if token contains structured response with component data
            //               let tokenContent = parsedData.token

            //               // Only try to parse as JSON if it looks like JSON (starts with { or [)
            //               if (typeof tokenContent === 'string' && tokenContent.trim().startsWith('{')) {
            //                 try {
            //                   const tokenData = JSON.parse(tokenContent)
            //                   if (tokenData.response_type === 'component' && tokenData.component_type) {
            //                     // Handle component response - show in overlay
            //                     console.log('Received component response:', tokenData)

            //                     // Store component data and show overlay
            //                     componentData.value = tokenData
            //                     showComponent.value = true

            //                     // Send the message to chat
            //                     signals.onResponse({
            //                       text: `${tokenData.message || ''} [点击查看详细图表]`
            //                     })

            //                     return
            //                   }
            //                 } catch (tokenParseError) {
            //                   // If token parsing fails, treat as regular text
            //                   console.log('Token parse error, treating as text:', tokenParseError.message)
            //                 }
            //               }

            //               // Regular text token (including markdown, code blocks, etc.)
            //               signals.onResponse({ text: tokenContent })
            //             } else {
            //               // Handle other structured data formats
            //               signals.onResponse({ text: JSON.stringify(parsedData) })
            //             }
            //           } else {
            //             // Handle non-object parsed data
            //             signals.onResponse({ text: String(parsedData) })
            //           }
            //         } catch (jsonParseError) {
            //           // If JSON parsing fails, treat as plain text
            //           signals.onResponse({ text: data })
            //         }
            //       }
            //     } catch (parseError) {
            //       console.error('Message parsing error:', parseError)
            //       signals.onResponse({ text: event.data || 'Unknown message' })
            //     }
            //   },
            //   onerror(error) {
            //     console.error('SSE Error:', error, 'Retry count was:', retryCount)

            //     // Abort the current request to prevent further retries
            //     if (abortController) {
            //       abortController.abort()
            //       abortController = null
            //     }

            //     // Enhanced error detection and user-friendly messages
            //     let errorMessage = '未知错误'
            //     let userMessage = '抱歉，发生了未知错误。请稍后重试。'

            //     if (error instanceof TypeError) {
            //       if (error.message.includes('Failed to fetch')) {
            //         // Check if this is a connection refused error (ERR_CONNECTION_REFUSED)
            //         // This typically happens when the server is not running or port is blocked
            //         const isConnectionRefused =
            //           error.message.includes('ERR_CONNECTION_REFUSED') ||
            //           error.message.includes('Connection refused') ||
            //           error.message.includes('ECONNREFUSED')

            //         if (isConnectionRefused) {
            //           console.log(
            //             'Connection refused error detected, checking reconnection attempts',
            //           )

            //           // For connection refused errors, only attempt reconnection if we haven't exceeded the limit
            //           if (reconnectAttempts < maxReconnectAttempts) {
            //             errorMessage = '服务器连接被拒绝，正在尝试重连...'
            //             userMessage = `服务器暂时无法连接，正在自动重连 (${reconnectAttempts + 1}/${maxReconnectAttempts})...`

            //             console.log('Triggering auto-reconnection for connection refused error')
            //             attemptReconnection(window.lastRequestBody)
            //           } else {
            //             // Don't trigger more reconnections if we've already reached the limit
            //             errorMessage = '服务器连接失败'
            //             userMessage =
            //               '服务器连接被拒绝且已达到最大重连次数。请检查服务器状态后刷新页面重试。'
            //             console.log(
            //               'Connection refused: Max reconnection attempts already reached, not triggering more reconnections',
            //             )
            //           }
            //         } else {
            //           // Other 'Failed to fetch' errors (network issues, CORS, etc.)
            //           if (retryCount >= maxRetries) {
            //             errorMessage = '网络连接多次重试失败，正在尝试重连...'
            //             userMessage = '网络连接不稳定，正在自动重连，请稍候...'

            //             // Trigger auto-reconnection for general network errors
            //             console.log(
            //               'Triggering auto-reconnection after max retries for general network error',
            //             )
            //             attemptReconnection(window.lastRequestBody)
            //           } else {
            //             // Network connectivity or CORS issue
            //             errorMessage = '网络连接失败或服务器不可达'
            //             userMessage =
            //               '无法连接到聊天服务器。请检查：\n1. 网络连接是否正常\n2. 服务器是否正在运行\n3. 服务器地址是否正确'
            //           }
            //         }
            //       } else {
            //         errorMessage = '网络请求错误'
            //         userMessage = '网络请求出现问题，请检查网络连接后重试。'
            //       }
            //     } else if (error.name === 'NetworkError') {
            //       errorMessage = '网络连接中断，正在尝试重连...'
            //       userMessage = '网络连接已中断，正在自动重连，请稍候...'

            //       // Trigger auto-reconnection for network errors
            //       attemptReconnection(window.lastRequestBody)
            //     } else if (error.name === 'AbortError') {
            //       errorMessage = '请求已取消'
            //       userMessage = '请求已被取消。'
            //     } else {
            //       // Log detailed error for debugging
            //       console.error('Detailed error info:', {
            //         name: error.name,
            //         message: error.message,
            //         stack: error.stack,
            //       })
            //       errorMessage = `服务器连接错误: ${error.name || 'Unknown'}`
            //       userMessage = '服务器暂时无法响应，请稍后重试。如问题持续，请联系技术支持。'
            //     }

            //     // Reset retry count after handling the error
            //     retryCount = 0

            //     signals.onResponse({
            //       error: errorMessage,
            //       text: userMessage,
            //     })

            //     // Ensure the connection is properly closed
            //     signals.onClose()
            //     isRequestInProgress = false // Reset request flag
            //   },
            //   onclose() {
            //     signals.onClose() // The stop button will be changed back to submit button
            //     abortController = null
            //     isRequestInProgress = false // Reset request flag
            //     retryCount = 0 // Reset retry count on connection close
            //   },
            // })
            const componentData = {
    "response_type": "component",
    "component_type": "TransTrendCard",
    "message": "查询到 20250920 至 20250926 的交易趋势数据，共 7 天的记录。",
    "component_data": {
        "trend_data": [
            {
                "transDate": "20250920",
                "transAmt": "0.0"
            },
            {
                "transDate": "20250921",
                "transAmt": "0.0"
            },
            {
                "transDate": "20250922",
                "transAmt": "6080.00"
            },
            {
                "transDate": "20250923",
                "transAmt": "10.00"
            },
            {
                "transDate": "20250924",
                "transAmt": "0.0"
            },
            {
                "transDate": "20250925",
                "transAmt": "0.0"
            },
            {
                "transDate": "20250926",
                "transAmt": "5000.00"
            }
        ],
        "begin_date": "20250920",
        "end_date": "20250926"
    },
    "raw_data": {
        "respCode": "90000",
        "respDesc": "交易成功",
        "requestSeqId": "bc5d99e8e0634d1c99cebb5eddaedc2b",
        "respSeq": "20250926155100141",
        "respData": {
            "timeControlDays": [
                {
                    "transDate": "20250920",
                    "transAmt": "0.0"
                },
                {
                    "transDate": "20250921",
                    "transAmt": "0.0"
                },
                {
                    "transDate": "20250922",
                    "transAmt": "6080.00"
                },
                {
                    "transDate": "20250923",
                    "transAmt": "10.00"
                },
                {
                    "transDate": "20250924",
                    "transAmt": "0.0"
                },
                {
                    "transDate": "20250925",
                    "transAmt": "0.0"
                },
                {
                    "transDate": "20250926",
                    "transAmt": "5000.00"
                }
            ]
        }
    }
}
            // 测试返回自定义组件
            setTimeout(() => {
              signals.onResponse({
                html: `<dynamic-component-renderer
                        message="${componentData.message}"
                        component-type="${componentData.component_type}"
                        component-data="${encodeURIComponent(JSON.stringify(componentData.component_data))}"
                      />`,
                // html: `<hello-world msg="1111" />`,
                role: 'ai'
              })
            }, 1000)

            // Set up stop button handler
            signals.stopClicked.listener = () => {
              if (abortController) {
                abortController.abort()
                abortController = null
              }
              // Reset request flag and retry count when user stops the request
              isRequestInProgress = false
              retryCount = 0
            }
          } catch (e) {
            console.error('Connection setup error:', e)

            // Reset all state variables on setup error
            retryCount = 0
            isRequestInProgress = false

            // Clean up abort controller if it exists
            if (abortController) {
              abortController.abort()
              abortController = null
            }

            // Provide clear error message instead of mock response
            signals.onResponse({
              error: '连接初始化失败',
              text: '抱歉，聊天服务初始化失败。请刷新页面重试。',
            })

            // Ensure the connection is properly closed
            signals.onClose()
            isRequestInProgress = false // Reset request flag
          }
        },
      }
    }

    onMounted(() => {
      // Setup connection after component is mounted
      setupDeepChatConnection()
    })

    onUnmounted(() => {
      // Clean up timers and connections
      if (reconnectTimer) {
        clearTimeout(reconnectTimer)
        reconnectTimer = null
      }

      if (abortController) {
        abortController.abort()
        abortController = null
      }

      // Reset all state
      isRequestInProgress = false
      retryCount = 0
      reconnectAttempts = 0
    })

    // Component state for dynamic rendering
    const showComponent = ref(false)
    const componentData = ref({})

    // Component handlers
    const closeComponent = () => {
      showComponent.value = false
      componentData.value = {}
    }

    const handleComponentAction = (action, data) => {
      console.log('Component action:', action, data)
      // Handle component interactions if needed
    }

    return {
      history,
      chatElementRef,
      messageStyles,
      textInput,
      submitButtonStyles,
      transList,
      showComponent,
      componentData,
      closeComponent,
      handleComponentAction,
    }
  },
}
</script>

<style lang="scss" scoped>
.home-view {
  font-family: sans-serif;
  text-align: center;
  justify-content: center;
  display: grid;
  width: 100vw;
  height: 100vh;
}

/* Component overlay styles */
.component-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.component-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  position: relative;
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  border-radius: 16px 16px 0 0;
}

.component-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #64748b;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background: #e2e8f0;
    color: #1e293b;
  }
}
</style>
