import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface AppState {
  isMobile: boolean
  sidebarOpen: boolean
  theme: 'light' | 'dark' | 'auto'
  language: string
}

export const useAppStore = defineStore('app', () => {
  // State
  const isMobile = ref(false)
  const sidebarOpen = ref(false)
  const theme = ref<'light' | 'dark' | 'auto'>('light')
  const language = ref('zh-CN')
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isDarkMode = computed(() => {
    if (theme.value === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    return theme.value === 'dark'
  })

  const deviceType = computed(() => {
    return isMobile.value ? 'mobile' : 'desktop'
  })

  // Actions
  const setMobile = (mobile: boolean) => {
    isMobile.value = mobile
    // Auto-close sidebar on mobile when switching to desktop
    if (!mobile && sidebarOpen.value) {
      sidebarOpen.value = false
    }
  }

  const toggleSidebar = () => {
    sidebarOpen.value = !sidebarOpen.value
  }

  const setSidebarOpen = (open: boolean) => {
    sidebarOpen.value = open
  }

  const setTheme = (newTheme: 'light' | 'dark' | 'auto') => {
    theme.value = newTheme
    updateThemeClass()
    // Save to localStorage
    localStorage.setItem('app-theme', newTheme)
  }

  const setLanguage = (lang: string) => {
    language.value = lang
    // Save to localStorage
    localStorage.setItem('app-language', lang)
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
  }

  const clearError = () => {
    error.value = null
  }

  // Theme management
  const updateThemeClass = () => {
    const root = document.documentElement
    if (isDarkMode.value) {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }

  // Initialize theme from localStorage
  const initializeTheme = () => {
    const savedTheme = localStorage.getItem('app-theme') as 'light' | 'dark' | 'auto' | null
    if (savedTheme) {
      theme.value = savedTheme
    }
    updateThemeClass()
  }

  // Initialize language from localStorage
  const initializeLanguage = () => {
    const savedLanguage = localStorage.getItem('app-language')
    if (savedLanguage) {
      language.value = savedLanguage
    }
  }

  // Mobile detection
  const detectMobile = () => {
    const userAgent = navigator.userAgent.toLowerCase()
    const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/.test(userAgent)
    const isSmallScreen = window.innerWidth <= 768
    
    setMobile(isMobileDevice || isSmallScreen)
  }

  // Initialize app
  const initialize = () => {
    initializeTheme()
    initializeLanguage()
    detectMobile()
    
    // Listen for system theme changes
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', () => {
        if (theme.value === 'auto') {
          updateThemeClass()
        }
      })
    }
    
    // Listen for window resize
    window.addEventListener('resize', detectMobile)
  }

  // Cleanup
  const cleanup = () => {
    window.removeEventListener('resize', detectMobile)
  }

  return {
    // State
    isMobile,
    sidebarOpen,
    theme,
    language,
    isLoading,
    error,
    
    // Getters
    isDarkMode,
    deviceType,
    
    // Actions
    setMobile,
    toggleSidebar,
    setSidebarOpen,
    setTheme,
    setLanguage,
    setLoading,
    setError,
    clearError,
    updateThemeClass,
    initialize,
    cleanup
  }
})