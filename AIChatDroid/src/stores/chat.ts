import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface ChatMessage {
  id: string
  text: string
  role: 'user' | 'ai' | 'system'
  timestamp: number
  type?: 'text' | 'image' | 'file' | 'error'
  metadata?: Record<string, any>
}

export interface ChatSession {
  id: string
  title: string
  messages: ChatMessage[]
  createdAt: number
  updatedAt: number
  model?: string
  settings?: Record<string, any>
}

export interface ChatSettings {
  model: string
  temperature: number
  maxTokens: number
  systemPrompt: string
  autoSave: boolean
  enableHistory: boolean
}

export const useChatStore = defineStore('chat', () => {
  // State
  const messages = ref<ChatMessage[]>([])
  const sessions = ref<ChatSession[]>([])
  const currentSessionId = ref<string | null>(null)
  const isTyping = ref(false)
  const isConnected = ref(false)
  const connectionError = ref<string | null>(null)
  
  const settings = ref<ChatSettings>({
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 2048,
    systemPrompt: '你是一个有用的AI助手。',
    autoSave: true,
    enableHistory: true
  })

  // Getters
  const currentSession = computed(() => {
    if (!currentSessionId.value) return null
    return sessions.value.find(session => session.id === currentSessionId.value) || null
  })

  const messageCount = computed(() => messages.value.length)
  
  const lastMessage = computed(() => {
    return messages.value.length > 0 ? messages.value[messages.value.length - 1] : null
  })

  const userMessages = computed(() => {
    return messages.value.filter(msg => msg.role === 'user')
  })

  const aiMessages = computed(() => {
    return messages.value.filter(msg => msg.role === 'ai')
  })

  const hasMessages = computed(() => messages.value.length > 0)

  // Actions
  const addMessage = (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    const newMessage: ChatMessage = {
      ...message,
      id: generateMessageId(),
      timestamp: Date.now()
    }
    
    messages.value.push(newMessage)
    
    // Update current session if exists
    if (currentSession.value) {
      currentSession.value.messages.push(newMessage)
      currentSession.value.updatedAt = Date.now()
    }
    
    // Auto-save if enabled
    if (settings.value.autoSave) {
      saveToLocalStorage()
    }
    
    return newMessage
  }

  const removeMessage = (messageId: string) => {
    const index = messages.value.findIndex(msg => msg.id === messageId)
    if (index > -1) {
      messages.value.splice(index, 1)
      
      // Update current session
      if (currentSession.value) {
        const sessionIndex = currentSession.value.messages.findIndex(msg => msg.id === messageId)
        if (sessionIndex > -1) {
          currentSession.value.messages.splice(sessionIndex, 1)
          currentSession.value.updatedAt = Date.now()
        }
      }
      
      if (settings.value.autoSave) {
        saveToLocalStorage()
      }
    }
  }

  const updateMessage = (messageId: string, updates: Partial<ChatMessage>) => {
    const message = messages.value.find(msg => msg.id === messageId)
    if (message) {
      Object.assign(message, updates)
      
      // Update in current session
      if (currentSession.value) {
        const sessionMessage = currentSession.value.messages.find(msg => msg.id === messageId)
        if (sessionMessage) {
          Object.assign(sessionMessage, updates)
          currentSession.value.updatedAt = Date.now()
        }
      }
      
      if (settings.value.autoSave) {
        saveToLocalStorage()
      }
    }
  }

  const clearMessages = () => {
    messages.value = []
    
    if (currentSession.value) {
      currentSession.value.messages = []
      currentSession.value.updatedAt = Date.now()
    }
    
    if (settings.value.autoSave) {
      saveToLocalStorage()
    }
  }

  // Session management
  const createSession = (title?: string): ChatSession => {
    const session: ChatSession = {
      id: generateSessionId(),
      title: title || `对话 ${sessions.value.length + 1}`,
      messages: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      model: settings.value.model,
      settings: { ...settings.value }
    }
    
    sessions.value.push(session)
    currentSessionId.value = session.id
    messages.value = []
    
    if (settings.value.autoSave) {
      saveToLocalStorage()
    }
    
    return session
  }

  const switchSession = (sessionId: string) => {
    const session = sessions.value.find(s => s.id === sessionId)
    if (session) {
      currentSessionId.value = sessionId
      messages.value = [...session.messages]
    }
  }

  const deleteSession = (sessionId: string) => {
    const index = sessions.value.findIndex(s => s.id === sessionId)
    if (index > -1) {
      sessions.value.splice(index, 1)
      
      // If deleting current session, switch to another or create new
      if (currentSessionId.value === sessionId) {
        if (sessions.value.length > 0) {
          switchSession(sessions.value[0].id)
        } else {
          currentSessionId.value = null
          messages.value = []
        }
      }
      
      if (settings.value.autoSave) {
        saveToLocalStorage()
      }
    }
  }

  const renameSession = (sessionId: string, newTitle: string) => {
    const session = sessions.value.find(s => s.id === sessionId)
    if (session) {
      session.title = newTitle
      session.updatedAt = Date.now()
      
      if (settings.value.autoSave) {
        saveToLocalStorage()
      }
    }
  }

  // Connection management
  const setConnected = (connected: boolean) => {
    isConnected.value = connected
    if (connected) {
      connectionError.value = null
    }
  }

  const setConnectionError = (error: string | null) => {
    connectionError.value = error
    if (error) {
      isConnected.value = false
    }
  }

  const setTyping = (typing: boolean) => {
    isTyping.value = typing
  }

  // Settings management
  const updateSettings = (newSettings: Partial<ChatSettings>) => {
    Object.assign(settings.value, newSettings)
    saveSettingsToLocalStorage()
  }

  const resetSettings = () => {
    settings.value = {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 2048,
      systemPrompt: '你是一个有用的AI助手。',
      autoSave: true,
      enableHistory: true
    }
    saveSettingsToLocalStorage()
  }

  // Utility functions
  const generateMessageId = (): string => {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  const generateSessionId = (): string => {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Persistence
  const saveToLocalStorage = () => {
    try {
      const data = {
        sessions: sessions.value,
        currentSessionId: currentSessionId.value,
        messages: messages.value
      }
      localStorage.setItem('chat-data', JSON.stringify(data))
    } catch (error) {
      console.error('Failed to save chat data:', error)
    }
  }

  const loadFromLocalStorage = () => {
    try {
      const saved = localStorage.getItem('chat-data')
      if (saved) {
        const data = JSON.parse(saved)
        sessions.value = data.sessions || []
        currentSessionId.value = data.currentSessionId || null
        messages.value = data.messages || []
      }
    } catch (error) {
      console.error('Failed to load chat data:', error)
    }
  }

  const saveSettingsToLocalStorage = () => {
    try {
      localStorage.setItem('chat-settings', JSON.stringify(settings.value))
    } catch (error) {
      console.error('Failed to save chat settings:', error)
    }
  }

  const loadSettingsFromLocalStorage = () => {
    try {
      const saved = localStorage.getItem('chat-settings')
      if (saved) {
        const savedSettings = JSON.parse(saved)
        Object.assign(settings.value, savedSettings)
      }
    } catch (error) {
      console.error('Failed to load chat settings:', error)
    }
  }

  // Export/Import
  const exportData = () => {
    return {
      sessions: sessions.value,
      settings: settings.value,
      exportedAt: Date.now()
    }
  }

  const importData = (data: any) => {
    try {
      if (data.sessions) {
        sessions.value = data.sessions
      }
      if (data.settings) {
        Object.assign(settings.value, data.settings)
      }
      
      // Switch to first session if available
      if (sessions.value.length > 0) {
        switchSession(sessions.value[0].id)
      }
      
      if (settings.value.autoSave) {
        saveToLocalStorage()
        saveSettingsToLocalStorage()
      }
    } catch (error) {
      console.error('Failed to import data:', error)
      throw new Error('导入数据失败')
    }
  }

  // Initialize
  const initialize = () => {
    loadSettingsFromLocalStorage()
    if (settings.value.enableHistory) {
      loadFromLocalStorage()
    }
    
    // Create initial session if none exists
    if (sessions.value.length === 0) {
      createSession('默认对话')
    } else if (currentSessionId.value) {
      // Load current session messages
      const session = sessions.value.find(s => s.id === currentSessionId.value)
      if (session) {
        messages.value = [...session.messages]
      }
    }
  }

  return {
    // State
    messages,
    sessions,
    currentSessionId,
    isTyping,
    isConnected,
    connectionError,
    settings,
    
    // Getters
    currentSession,
    messageCount,
    lastMessage,
    userMessages,
    aiMessages,
    hasMessages,
    
    // Actions
    addMessage,
    removeMessage,
    updateMessage,
    clearMessages,
    createSession,
    switchSession,
    deleteSession,
    renameSession,
    setConnected,
    setConnectionError,
    setTyping,
    updateSettings,
    resetSettings,
    exportData,
    importData,
    initialize
  }
})