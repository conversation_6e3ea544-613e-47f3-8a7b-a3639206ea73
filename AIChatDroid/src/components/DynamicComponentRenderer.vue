<template>
  <div class="dynamic-component-renderer">
    <div v-if="!componentType || componentType === 'TextOnly'" class="text-message">
    </div>

    <div v-else class="component-wrapper">
      <div v-if="message" class="component-message">
        {{ message }}
      </div>

      <component
        :is="componentType"
        v-bind="componentProps"
        @component-action="handleComponentAction"
        class="dynamic-component"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent } from 'vue'
import TransListCard from './TransListCard.vue'
import TransTrendCard from './TransTrendCard.vue'

// 动态组件映射
const componentMap = {
  'TransListCard': TransListCard,
  'TransTrendCard': TransTrendCard,
}

interface Props {
  message?: string
  componentType?: string
  componentData?: string
}

const props = withDefaults(defineProps<Props>(), {
  message: '',
  componentType: 'TextOnly',
  componentData: ''
})

const emit = defineEmits<{
  componentAction: [action: string, data: any]
}>()

// 获取动态组件
const componentType = computed(() => {
  if (!props.componentType || props.componentType === 'TextOnly') {
    return null
  }
  return componentMap[props.componentType as keyof typeof componentMap] || null
})

// 计算组件属性
const componentProps = computed(() => {
  if (!props.componentData) return {}
  const dataStr = decodeURIComponent(props.componentData)
  const data = JSON.parse(dataStr)
  switch (props.componentType) {
    case 'TransListCard':
      return {
        transInfos: data.transInfos || [],
        transDate: data.trans_date || '',
        totalAmount: data.total_amount || '0',
        totalRefundAmt: data.total_refund_amt || '0',
        transNumber: data.trans_number || '0',
        totalFeeAmt: data.total_fee_amt || '0'
      }
    case 'TransTrendCard':
      return {
        transList: data.trend_data || [],
        beginDate: data.begin_date || '',
        endDate: data.end_date || ''
      }
    default:
      return data
  }
})

// 处理组件事件
const handleComponentAction = (action: string, data: any) => {
  emit('componentAction', action, data)
}
</script>

<style lang="scss" scoped>
.dynamic-component-renderer {
  width: 100%;

  .text-message {
    padding: 12px 16px;
    background: #f8fafc;
    border-radius: 12px;
    color: #334155;
    line-height: 1.5;
    border: 1px solid #e2e8f0;
  }

  .component-wrapper {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .component-message {
      padding: 8px 12px;
      background: #f1f5f9;
      border-radius: 8px;
      color: #475569;
      font-size: 14px;
      line-height: 1.4;
      border-left: 3px solid #3b82f6;
    }

    .dynamic-component {
      max-width: 100%;
      overflow: hidden;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dynamic-component-renderer {
    .component-wrapper {
      gap: 8px;

      .component-message {
        padding: 6px 10px;
        font-size: 13px;
      }
    }
  }
}
</style>
