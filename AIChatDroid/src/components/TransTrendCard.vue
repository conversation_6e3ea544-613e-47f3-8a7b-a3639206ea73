<!--
 * @Author: tibin.zhang <EMAIL>
 * @Date: 2025-09-23 15:24:18
 * @LastEditors: tibin.zhang <EMAIL>
 * @LastEditTime: 2025-09-24 17:44:16
 * @FilePath: /matrix-rn/AIChatDroid/src/components/TransTrendCard.vue
 * @Description:
 *
 * Copyright © 2025 by PnR Data Service Co.,Ltd, All Rights Reserved.
-->
<template>
  <div class="trans-trend-card-container">
    <div class="card-header">
      <div class="date-range">交易日期：{{ formatDateRange }}</div>
    </div>
    <div class="chart-container">
      <div class="chart-content">
        <div class="bars-container">
          <div
            v-for="(item, index) in processedData"
            :key="index"
            class="bar-item"
            @click="showTooltip(item, index, $event)"
          >
            <div class="bar-wrapper">
              <div
                class="bar"
                :class="getBarClass(item.transAmt)"
                :style="getBarStyle(item.transAmt)"
              ></div>
            </div>
            <div class="date-label">{{ formatChartDate(item.transDate) }}</div>
          </div>
        </div>
        <!-- Tooltip 组件 -->
        <div v-if="activeTooltip" class="tooltip" :style="tooltipStyle">
          <div class="tooltip-content">
            <div class="tooltip-date">{{ formatTooltipDate(activeTooltip.transDate) }}</div>
            <div class="tooltip-amount" :class="getAmountClass(activeTooltip.transAmt)">
              {{ formatTooltipAmount(activeTooltip.transAmt) }}
            </div>
          </div>
          <div class="tooltip-arrow"></div>
        </div>
      </div>
    </div>
    <div class="more-trans-btn" @click="moreTransClick()">查看更多>>></div>
  </div>
</template>

<script>
export default {
  props: {
    transList: {
      type: Array,
      default() {
        return []
      },
    },
    beginDate: {
      type: String,
      default: '',
    },
    endDate: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      activeTooltip: null,
      tooltipPosition: { x: 0, y: 0 },
    }
  },
  computed: {
    formatDateRange() {
      if (this.beginDate && this.endDate) {
        return `${this.formatDisplayDate(this.beginDate)} - ${this.formatDisplayDate(this.endDate)}`
      }
      return '交易趋势'
    },
    processedData() {
      // 处理数据，确保有值用于显示
      if (this.transList && this.transList.length > 0) {
        return this.transList.map((item) => ({
          ...item,
          transAmt: parseFloat(item.transAmt) || 0,
        }))
      }
      // 测试数据用于调试
      return [
        { transDate: '20250920', transAmt: 150.5 },
        { transDate: '20250921', transAmt: -80.3 },
        { transDate: '20250922', transAmt: 200.0 },
        { transDate: '20250923', transAmt: -45.2 },
        { transDate: '20250924', transAmt: 0 },
      ]
    },
    maxAmount() {
      if (this.processedData.length === 0) return 100
      const amounts = this.processedData.map((item) => Math.abs(item.transAmt))
      return Math.max(...amounts, 100) // 确保最小高度为100
    },
    tooltipStyle() {
      return {
        left: `${this.tooltipPosition.x}px`,
        top: `${this.tooltipPosition.y}px`,
        display: this.activeTooltip ? 'block' : 'none',
      }
    },
  },
  methods: {
    formatDisplayDate(dateStr) {
      if (dateStr.length === 8) {
        return `${dateStr.substring(0, 4)}/${dateStr.substring(4, 6)}/${dateStr.substring(6, 8)}`
      }
      return dateStr
    },
    formatChartDate(dateStr) {
      if (dateStr.length === 8) {
        return `${dateStr.substring(4, 6)}/${dateStr.substring(6, 8)}`
      }
      return dateStr
    },
    formatTooltipDate(dateStr) {
      if (dateStr.length === 8) {
        return `${dateStr.substring(0, 4)}年${dateStr.substring(4, 6)}月${dateStr.substring(6, 8)}日`
      }
      return dateStr
    },
    formatAmount(amount) {
      const num = parseFloat(amount)
      if (num === 0) return '0'
      if (num > 0) return `+${num.toFixed(2)}`
      return num.toFixed(2)
    },
    formatTooltipAmount(amount) {
      const num = parseFloat(amount)
      if (num === 0) return '0.00'
      if (num > 0) return `+${num.toFixed(2)}`
      return num.toFixed(2)
    },
    getBarStyle(amount) {
      const num = parseFloat(amount)
      if (num === 0) {
        return {
          height: '2px',
          backgroundColor: '#d9d9d9',
        }
      }

      const absAmount = Math.abs(num)
      const height = Math.max((absAmount / this.maxAmount) * 100, 8)
      return {
        height: `${height}px`,
        backgroundColor: num > 0 ? '#52c41a' : '#ff4d4f',
        display: 'block',
      }
    },
    getBarClass(amount) {
      const num = parseFloat(amount)
      if (num > 0) return 'bar-positive'
      if (num < 0) return 'bar-negative'
      return 'bar-zero'
    },
    getAmountClass(amount) {
      const num = parseFloat(amount)
      if (num > 0) return 'amount-positive'
      if (num < 0) return 'amount-negative'
      return 'amount-zero'
    },
    showTooltip(item, index, event) {
      // 如果点击的是当前已激活的tooltip，则关闭它
      if (this.activeTooltip && this.activeTooltip.transDate === item.transDate) {
        this.activeTooltip = null
        return
      }

      // 设置激活的tooltip数据
      this.activeTooltip = item

      // 计算tooltip位置
      const barElement = event.currentTarget
      const barRect = barElement.getBoundingClientRect()
      const containerRect = this.$el.querySelector('.chart-content').getBoundingClientRect()

      // 计算相对于图表容器的位置
      this.tooltipPosition = {
        x: barRect.left - containerRect.left + barRect.width / 2,
        y: barRect.top - containerRect.top - 10, // 在柱子上方10px
      }
    },
    hideTooltip() {
      this.activeTooltip = null
    },
    // 查看更多交易
    moreTransClick() {
      console.log('moreTransClick')
    },
  },
  mounted() {
    console.log('TransTrendCard mounted')
    console.log('transList:', this.transList)

    // 点击图表外部时隐藏tooltip
    document.addEventListener('click', (e) => {
      if (!this.$el.contains(e.target)) {
        this.hideTooltip()
      }
    })
  },
  beforeDestroy() {
    // 移除事件监听
    document.removeEventListener('click', this.hideTooltip)
  },
}
</script>

<style lang="scss" scoped>
.trans-trend-card-container {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  /* 确保圆角显示完整 */
  position: relative;

  .card-header {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    padding: 14px;

    .date-range {
      font-size: 16px;
      font-weight: 600;
      color: white;
    }
  }

  .more-trans-btn {
    margin: 0 14px 14px;
    font-size: 14px;
    color: #6a11cb;
  }

  .chart-container {
    .chart-content {
      position: relative;
      height: 200px;
      padding: 14px;
      /* 为图表区域添加内边距 */

      .bars-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        align-items: flex-end;
        height: 100%;
        position: relative;
      }

      .zero-line {
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        bottom: 80px;
        /* 调整零线位置 */
        background: #e0e0e0;
        z-index: 1;
        transform: translateY(-1px);
      }

      /* Tooltip 样式 */
      .tooltip {
        position: absolute;
        z-index: 10;
        transform: translateX(-50%);
        margin-top: -10px;

        .tooltip-content {
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 8px 12px;
          border-radius: 6px;
          font-size: 12px;
          white-space: nowrap;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

          .tooltip-date {
            font-weight: 500;
            margin-bottom: 4px;
          }

          .tooltip-amount {
            font-weight: 600;
            font-size: 14px;

            &.amount-positive {
              color: #95de64;
            }

            &.amount-negative {
              color: #ff7875;
            }

            &.amount-zero {
              color: #d9d9d9;
            }
          }
        }

        .tooltip-arrow {
          position: absolute;
          bottom: -6px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 6px solid transparent;
          border-right: 6px solid transparent;
          border-top: 6px solid rgba(0, 0, 0, 0.8);
        }
      }
    }
  }

  .bar-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    max-width: 60px;
    cursor: pointer;
    /* 添加指针样式表明可点击 */
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }

    .bar-wrapper {
      height: 120px;
      display: flex;
      align-items: flex-end;
      justify-content: center;
      margin-bottom: 8px;
      /* 减少底部间距 */
      align-items: flex-end;
      /* 柱状图底部对齐 */

      .bar {
        width: 30px;
        /* 调整柱状图宽度 */
        min-height: 0px;
        /* 最小高度 */
        transition: all 0.3s ease;

        &.bar-positive {
          background: linear-gradient(to top, #1890ff, #69c0ff);
          border-radius: 4px 4px 0 0;
        }

        &.bar-negative {
          background: linear-gradient(to bottom, #ff4d4f, #ff7875) !important;
          border-radius: 0 0 4px 4px;
        }

        &.bar-zero {
          background: #d9d9d9 !important;
          border-radius: 2px;
        }
      }
    }

    .date-label {
      font-size: 12px;
      color: #666;
    }

    .amount-label {
      font-size: 11px;
      font-weight: 500;

      &.amount-positive {
        color: #52c41a;
      }

      &.amount-negative {
        color: #ff4d4f;
      }

      &.amount-zero {
        color: #999;
      }
    }
  }
}
</style>
