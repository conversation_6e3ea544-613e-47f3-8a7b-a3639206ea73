<template>
  <div class="deep-chat-wrapper" :class="wrapperClass">
    <div ref="deepChatRef" class="deep-chat-container" :class="containerClass"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import type { Ref } from 'vue'

// Deep Chat types
interface DeepChatInstance {
  connect?: object
  demo?: boolean
  history?: Array<{ text: string; role: string; isStreaming?: boolean }>
  style?: object
  textInput?: object
  messageStyles?: object
  directConnection?: object
  submitButtonStyles?: object
  microphone?: object
  [key: string]: any
}

// Message interface for streaming
interface StreamingMessage {
  text: string
  role: 'user' | 'ai' | 'system'
  isStreaming?: boolean
  timestamp?: number
}

// Props interface
interface Props {
  connect?: object
  demo?: boolean
  history?: Array<{ text: string; role: string }>
  style?: object
  textInput?: object
  messageStyles?: object
  directConnection?: object
  submitButtonStyles?: object
  microphone?: object
  customButtons?: Array<any>
  isMobile?: boolean
  customClass?: string
  enableStreaming?: boolean
  streamingSpeed?: number
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  demo: true,
  isMobile: false,
  customClass: '',
  enableStreaming: true,
  streamingSpeed: 50,
  history: () => [{ text: '你好！我是AI助手，有什么可以帮助你的吗？', role: 'ai' }],
  customButtons: () => [],
})

// Define emits
const emit = defineEmits<{
  onNewMessage: [message: { text: string; role: 'user' | 'ai' | 'system' }]
  onConnect: []
  onDisconnect: []
  onError: [error: any]
}>()

// Refs
const deepChatRef: Ref<HTMLElement | null> = ref(null)
const deepChatInstance: Ref<DeepChatInstance | null> = ref(null)
const isStreaming = ref(false)
const currentStreamingMessage = ref('')
const streamingTimeoutId = ref<number | null>(null)
const messageHistory = ref<StreamingMessage[]>(props.history.map(msg => ({
  ...msg,
  role: msg.role as 'user' | 'ai' | 'system',
  timestamp: Date.now()
})))

// Computed classes
const wrapperClass = computed(() => {
  return ['w-full', props.isMobile ? 'h-screen' : 'h-96', props.customClass]
    .filter(Boolean)
    .join(' ')
})

const containerClass = computed(() => {
  return props.isMobile ? 'deep-chat-mobile' : 'deep-chat-desktop'
})

// Default styles for mobile optimization
const defaultMobileStyle = {
  borderRadius: '0px',
  border: 'none',
  width: '100%',
  height: '100%',
  background: '#ffffff',
  fontFamily: 'Inter, system-ui, -apple-system, sans-serif',
}

const defaultDesktopStyle = {
  borderRadius: '12px',
  border: '1px solid #e5e7eb',
  width: '100%',
  height: '100%',
  background: '#ffffff',
  boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  fontFamily: 'Inter, system-ui, -apple-system, sans-serif',
}

// Default message styles
const defaultMessageStyles = {
  default: {
    shared: {
      bubble: {
        backgroundColor: 'unset',
        marginTop: '8px',
        marginBottom: '8px',
        padding: props.isMobile ? '12px 16px' : '10px 14px',
        maxWidth: props.isMobile ? '90%' : '85%',
        fontSize: '14px',
        lineHeight: '1.5',
        fontFamily: 'inherit',
        wordBreak: 'break-word',
        transition: 'all 0.2s ease-in-out',
      },
    },
    user: {
      bubble: {
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: '#ffffff',
        marginLeft: 'auto',
        borderRadius: '18px 18px 4px 18px',
        boxShadow: '0 2px 8px rgba(102, 126, 234, 0.3)',
      },
    },
    ai: {
      bubble: {
        background: '#f8fafc',
        color: '#1e293b',
        marginRight: 'auto',
        borderRadius: '18px 18px 18px 4px',
        border: '1px solid #e2e8f0',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      },
    },
    system: {
      bubble: {
        background: '#fef3c7',
        color: '#92400e',
        margin: '0 auto',
        borderRadius: '12px',
        border: '1px solid #fbbf24',
        fontSize: '13px',
        maxWidth: '70%',
      },
    },
  },
}

// Default text input styles - moved to computed to handle props properly
const getDefaultTextInput = () => ({
  styles: {
    container: {
      borderRadius: '25px',
      border: '1px solid #d1d5db',
      padding: props.isMobile ? '12px 16px' : '8px 16px',
      margin: props.isMobile ? '12px' : '16px',
      backgroundColor: '#ffffff',
      boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      transition: 'all 0.2s ease-in-out',
    },
    text: {
      padding: '8px 12px',
      fontSize: '14px',
      color: '#374151',
      fontFamily: 'inherit',
      lineHeight: '1.5',
    },
    focus: {
      borderColor: '#667eea',
      boxShadow: '0 0 0 3px rgba(102, 126, 234, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    },
  },
  placeholder: {
    text: '输入消息...',
    style: {
      color: '#9ca3af',
      fontSize: '14px',
      fontStyle: 'normal',
    },
  },
  characterLimit: 2000,
  disabled: false,
})

// Default submit button styles
const getDefaultSubmitButtonStyles = () => ({
  submit: {
    container: {
      default: {
        backgroundColor: '#667eea',
        borderRadius: '50%',
        padding: '8px',
        border: 'none',
        cursor: 'pointer',
        transition: 'all 0.2s ease-in-out',
        boxShadow: '0 2px 4px rgba(102, 126, 234, 0.3)',
      },
      hover: {
        backgroundColor: '#5a67d8',
        transform: 'scale(1.05)',
        boxShadow: '0 4px 8px rgba(102, 126, 234, 0.4)',
      },
      disabled: {
        backgroundColor: '#d1d5db',
        cursor: 'not-allowed',
        transform: 'none',
        boxShadow: 'none',
      },
    },
    svg: {
      content: '→',
      styles: {
        default: {
          width: '16px',
          height: '16px',
          fill: '#ffffff',
        },
      },
    },
  },
})

// Default custom buttons configuration
const getDefaultCustomButtons = () => [
  {
    position: 'inside-left',
    styles: {
      button: {
        default: {
          container: {
            default: {
              backgroundColor: '#f8fafc',
              border: '1px solid #e2e8f0',
              borderRadius: '8px',
              padding: '6px',
              margin: '2px',
              cursor: 'pointer',
              transition: 'all 0.2s ease-in-out',
            },
            hover: {
              backgroundColor: '#f1f5f9',
              borderColor: '#cbd5e1',
              transform: 'scale(1.05)',
            },
            active: {
              backgroundColor: '#e2e8f0',
              borderColor: '#94a3b8',
            },
          },
          svg: {
            content: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M3 6h18l-2 13H5L3 6z"/>
              <path d="M3 6l2-2h14l2 2"/>
              <path d="M10 11v6"/>
              <path d="M14 11v6"/>
            </svg>`,
            styles: {
              default: {
                width: '16px',
                height: '16px',
                color: '#64748b',
              },
            },
          },
          text: {
            content: '清除',
            styles: {
              default: {
                fontSize: '12px',
                color: '#64748b',
                marginLeft: '4px',
              },
            },
          },
        },
      },
    },
  },
  {
    position: 'inside-left',
    styles: {
      button: {
        default: {
          container: {
            default: {
              backgroundColor: '#f8fafc',
              border: '1px solid #e2e8f0',
              borderRadius: '8px',
              padding: '6px',
              margin: '2px',
              cursor: 'pointer',
              transition: 'all 0.2s ease-in-out',
            },
            hover: {
              backgroundColor: '#f1f5f9',
              borderColor: '#cbd5e1',
              transform: 'scale(1.05)',
            },
            active: {
              backgroundColor: '#e2e8f0',
              borderColor: '#94a3b8',
            },
          },
          svg: {
            content: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
              <line x1="12" y1="19" x2="12" y2="23"/>
              <line x1="8" y1="23" x2="16" y2="23"/>
            </svg>`,
            styles: {
              default: {
                width: '16px',
                height: '16px',
                color: '#64748b',
              },
            },
          },
          text: {
            content: '语音',
            styles: {
              default: {
                fontSize: '12px',
                color: '#64748b',
                marginLeft: '4px',
              },
            },
          },
        },
      },
    },
  },
  {
    position: 'inside-left',
    styles: {
      button: {
        default: {
          container: {
            default: {
              backgroundColor: '#f8fafc',
              border: '1px solid #e2e8f0',
              borderRadius: '8px',
              padding: '6px',
              margin: '2px',
              cursor: 'pointer',
              transition: 'all 0.2s ease-in-out',
            },
            hover: {
              backgroundColor: '#f1f5f9',
              borderColor: '#cbd5e1',
              transform: 'scale(1.05)',
            },
            active: {
              backgroundColor: '#e2e8f0',
              borderColor: '#94a3b8',
            },
          },
          svg: {
            content: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="3"/>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
            </svg>`,
            styles: {
              default: {
                width: '16px',
                height: '16px',
                color: '#64748b',
              },
            },
          },
          text: {
            content: '设置',
            styles: {
              default: {
                fontSize: '12px',
                color: '#64748b',
                marginLeft: '4px',
              },
            },
          },
        },
      },
    },
  },
]

// Custom button click handler
const handleCustomButtonClick = (buttonIndex: number, state: string) => {
  console.log(`Custom button ${buttonIndex} clicked with state: ${state}`)
  
  switch (buttonIndex) {
    case 0: // Clear button
      clearHistory()
      break
    case 1: // Voice button
      toggleVoiceInput()
      break
    case 2: // Settings button
      openSettings()
      break
    default:
      console.log(`Unknown button index: ${buttonIndex}`)
  }
  
  // Return new state for button (toggle between 'active' and 'default')
  return state === 'active' ? 'default' : 'active'
}

// Clear chat history
const clearHistory = () => {
  messageHistory.value = [{ text: '你好！我是AI助手，有什么可以帮助你的吗？', role: 'ai', timestamp: Date.now() }]
  updateDeepChatHistory()
  console.log('Chat history cleared')
}

// Toggle voice input (placeholder)
const toggleVoiceInput = () => {
  console.log('Voice input toggled')
  // TODO: Implement voice input functionality
}

// Open settings (placeholder)
const openSettings = () => {
  console.log('Settings opened')
  // TODO: Implement settings functionality
}

// Mock AI response generator
const generateMockResponse = (userMessage: string): string => {
  const responses = [
    `我理解您提到的"${userMessage}"。这是一个很有趣的话题，让我为您详细分析一下相关的要点和可能的解决方案。`,
    `关于"${userMessage}"这个问题，我可以从多个角度来帮助您。首先，我们需要考虑的是具体的应用场景和您的实际需求。`,
    `您好！针对"${userMessage}"，我建议我们可以通过以下几个步骤来逐步解决：第一步是分析现状，第二步是制定策略，第三步是具体实施。`,
    `这是一个很好的问题！"${userMessage}"涉及到多个方面的考虑。让我为您提供一些专业的建议和最佳实践方案。`,
    `感谢您的提问！关于"${userMessage}"，我可以为您提供详细的解答。这个话题确实需要我们深入探讨一下相关的技术细节和实现方法。`
  ]
  
  return responses[Math.floor(Math.random() * responses.length)]
}

// Streaming message handler
const streamMessage = async (message: string, role: 'ai' | 'system' = 'ai') => {
  if (!props.enableStreaming) {
    // Non-streaming mode: add message directly
    const newMessage: StreamingMessage = {
      text: message,
      role,
      timestamp: Date.now()
    }
    messageHistory.value.push(newMessage)
    updateDeepChatHistory()
    return
  }

  isStreaming.value = true
  currentStreamingMessage.value = ''
  
  // Add empty message to history for streaming
  const streamingMessage: StreamingMessage = {
    text: '',
    role,
    isStreaming: true,
    timestamp: Date.now()
  }
  messageHistory.value.push(streamingMessage)
  
  // Stream the message character by character
  for (let i = 0; i < message.length; i++) {
    await new Promise(resolve => {
      streamingTimeoutId.value = window.setTimeout(() => {
        currentStreamingMessage.value += message[i]
        // Update the last message in history
        const lastMessage = messageHistory.value[messageHistory.value.length - 1]
        if (lastMessage && lastMessage.isStreaming) {
          lastMessage.text = currentStreamingMessage.value
        }
        updateDeepChatHistory()
        resolve(void 0)
      }, props.streamingSpeed)
    })
  }
  
  // Mark streaming as complete
  const lastMessage = messageHistory.value[messageHistory.value.length - 1]
  if (lastMessage && lastMessage.isStreaming) {
    lastMessage.isStreaming = false
  }
  
  isStreaming.value = false
  currentStreamingMessage.value = ''
}

// Update DeepChat history
const updateDeepChatHistory = () => {
  if (deepChatInstance.value) {
    deepChatInstance.value.history = messageHistory.value.map(msg => ({
      text: msg.text,
      role: msg.role
    }))
  }
}

// Handle new user message
const handleNewMessage = async (message: { text: string; role: 'user' | 'ai' | 'system' }) => {
  // Add user message to history
  const userMessage: StreamingMessage = {
    ...message,
    timestamp: Date.now()
  }
  messageHistory.value.push(userMessage)
  updateDeepChatHistory()
  
  // Emit the message event
  emit('onNewMessage', message)
  
  // Generate and stream AI response if it's a user message
  if (message.role === 'user') {
    // Add a small delay to simulate processing
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const aiResponse = generateMockResponse(message.text)
    await streamMessage(aiResponse, 'ai')
  }
}

// Initialize Deep Chat
const initializeDeepChat = async () => {
  if (!deepChatRef.value) {
    console.warn('DeepChat ref not available')
    return
  }

  try {
    // Import Deep Chat dynamically
    await import('deep-chat')

    // Create deep-chat element
    const deepChatElement = document.createElement('deep-chat') as any

    // Set properties
    if (props.connect) deepChatElement.connect = props.connect
    if (props.demo !== undefined) deepChatElement.demo = props.demo
    if (props.history) deepChatElement.history = props.history
    if (props.directConnection) deepChatElement.directConnection = props.directConnection

    // Set styles
    const finalStyle = {
      ...(props.isMobile ? defaultMobileStyle : defaultDesktopStyle),
      ...props.style,
    }
    deepChatElement.style = JSON.stringify(finalStyle)

    // Set message styles
    const finalMessageStyles = {
      ...defaultMessageStyles,
      ...props.messageStyles,
    }
    deepChatElement.messageStyles = JSON.stringify(finalMessageStyles)

    // Set text input styles
    const finalTextInput = {
      ...getDefaultTextInput(),
      ...props.textInput,
    }
    deepChatElement.textInput = finalTextInput

    // Set submit button styles
    const finalSubmitButtonStyles = {
      ...getDefaultSubmitButtonStyles(),
      ...props.submitButtonStyles,
    }
    deepChatElement.submitButtonStyles = finalSubmitButtonStyles

    // Set microphone if provided
    if (props.microphone) {
      deepChatElement.microphone = JSON.stringify(props.microphone)
    }

    // Set custom buttons
    const finalCustomButtons = props.customButtons.length > 0 ? props.customButtons : getDefaultCustomButtons()
    if (finalCustomButtons.length > 0) {
      // Add onClick handlers to custom buttons
      const customButtonsWithHandlers = finalCustomButtons.map((button, index) => ({
        ...button,
        onClick: (state: string) => handleCustomButtonClick(index, state)
      }))
      deepChatElement.customButtons = customButtonsWithHandlers
    }

    // Set additional properties for better UX
    deepChatElement.auxiliaryStyle = JSON.stringify({
      loading: {
        bubble: {
          backgroundColor: '#f3f4f6',
          border: '1px solid #e5e7eb',
          borderRadius: '18px',
          padding: '12px 16px',
          animation: 'pulse 1.5s ease-in-out infinite',
        },
      },
    })

    // Add event listeners
    deepChatElement.addEventListener('new-message', (event: CustomEvent) => {
      handleNewMessage(event.detail)
    })

    deepChatElement.addEventListener('connect', () => {
      emit('onConnect')
    })

    deepChatElement.addEventListener('disconnect', () => {
      emit('onDisconnect')
    })

    deepChatElement.addEventListener('error', (event: CustomEvent) => {
      emit('onError', event.detail)
    })

    // Clear container and append element
    deepChatRef.value.innerHTML = ''
    deepChatRef.value.appendChild(deepChatElement)

    deepChatInstance.value = deepChatElement
  } catch (error) {
    console.error('Failed to initialize Deep Chat:', error)
    
    // Create fallback UI
    if (deepChatRef.value) {
      deepChatRef.value.innerHTML = `
        <div class="flex items-center justify-center h-full p-4 bg-red-50 border border-red-200 rounded-lg">
          <div class="text-center">
            <div class="text-red-600 mb-2">聊天组件初始化失败</div>
            <div class="text-sm text-red-500">请刷新页面重试</div>
          </div>
        </div>
      `
    }
    
    emit('onError', error)
  }
}

// Watch for prop changes
watch(
  () => props.history,
  (newHistory) => {
    if (deepChatInstance.value && newHistory) {
      deepChatInstance.value.history = newHistory
    }
  },
  { deep: true },
)

watch(
  () => props.isMobile,
  () => {
    // Reinitialize when mobile state changes
    initializeDeepChat()
  },
)

// Lifecycle hooks
onMounted(() => {
  initializeDeepChat()
})

onUnmounted(() => {
  try {
    // Clear streaming timeout
    if (streamingTimeoutId.value) {
      clearTimeout(streamingTimeoutId.value)
      streamingTimeoutId.value = null
    }
    
    if (deepChatInstance.value) {
      // Remove event listeners
      deepChatInstance.value.removeEventListener?.('new-message', () => {})
      deepChatInstance.value.removeEventListener?.('connect', () => {})
      deepChatInstance.value.removeEventListener?.('disconnect', () => {})
      deepChatInstance.value.removeEventListener?.('error', () => {})
      
      deepChatInstance.value = null
    }
    
    // Clear container
    if (deepChatRef.value) {
      deepChatRef.value.innerHTML = ''
    }
    
    // Reset streaming state
    isStreaming.value = false
    currentStreamingMessage.value = ''
  } catch (error) {
    console.warn('Error during component cleanup:', error)
  }
})

// Expose methods for parent component
defineExpose({
  sendMessage: async (message: string, role: 'user' | 'ai' | 'system' = 'user') => {
    const newMessage = { text: message, role }
    await handleNewMessage(newMessage)
  },
  streamMessage: async (message: string, role: 'ai' | 'system' = 'ai') => {
    await streamMessage(message, role)
  },
  clearHistory: () => {
    clearHistory()
  },
  stopStreaming: () => {
    if (streamingTimeoutId.value) {
      clearTimeout(streamingTimeoutId.value)
      streamingTimeoutId.value = null
    }
    isStreaming.value = false
    currentStreamingMessage.value = ''
  },
  getHistory: () => messageHistory.value,
  isStreamingActive: () => isStreaming.value,
  getInstance: () => deepChatInstance.value,
  // Custom button methods
  handleCustomButtonClick: (buttonIndex: number, state: string) => handleCustomButtonClick(buttonIndex, state),
  toggleVoiceInput: () => toggleVoiceInput(),
  openSettings: () => openSettings(),
})
</script>

<style scoped>
.deep-chat-wrapper {
  @apply relative overflow-hidden;
  transition: all 0.3s ease-in-out;
}

.deep-chat-container {
  @apply w-full h-full;
  position: relative;
}

/* Ensure deep-chat element fills container */
.deep-chat-container :deep(deep-chat) {
  width: 100% !important;
  height: 100% !important;
  display: block;
  font-family:
    Inter,
    system-ui,
    -apple-system,
    sans-serif;
}

/* Loading animation */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Smooth transitions for all interactive elements */
.deep-chat-container :deep(*) {
  transition: all 0.2s ease-in-out;
}

/* Enhanced message bubble animations */
.deep-chat-container :deep(.message-bubble) {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Streaming message effects */
.deep-chat-container :deep(.streaming-message) {
  position: relative;
}

.deep-chat-container :deep(.streaming-message::after) {
  content: '|';
  animation: blink 1s infinite;
  color: #667eea;
  font-weight: bold;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* Loading indicator for AI responses */
.deep-chat-container :deep(.ai-thinking) {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: #f3f4f6;
  border-radius: 12px;
  margin: 4px 0;
}

.deep-chat-container :deep(.ai-thinking::before) {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #9ca3af;
  animation: thinking 1.4s infinite ease-in-out both;
}

.deep-chat-container :deep(.ai-thinking::after) {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #9ca3af;
  animation: thinking 1.4s infinite ease-in-out both;
  animation-delay: 0.16s;
}

@keyframes thinking {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .deep-chat-container :deep(deep-chat) {
    border-radius: 0 !important;
    border: none !important;
  }

  .deep-chat-wrapper {
    height: 100vh;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .deep-chat-wrapper {
    background-color: #1f2937;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .deep-chat-container :deep(deep-chat) {
    border: 2px solid currentColor !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .deep-chat-wrapper,
  .deep-chat-container :deep(*) {
    transition: none !important;
    animation: none !important;
  }
}
</style>
