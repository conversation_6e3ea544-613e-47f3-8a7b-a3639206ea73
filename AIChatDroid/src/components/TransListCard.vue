<template>
  <div class="trans-list-card-container">
    <!-- Header with date range -->
    <div class="card-header">
      <div class="date-range">交易日期：{{ formatDateRange }}</div>
      <div class="total-count">共 {{ totalCount }} 笔交易</div>
    </div>

    <!-- Summary section -->
    <div class="summary-section">
      <div class="summary-row">
        <div class="summary-item">
          <div class="summary-label">收款金额</div>
          <div class="summary-value positive">+¥{{ formatCurrency(summary.totalAmount) }}</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">退款金额</div>
          <div class="summary-value negative">-¥{{ formatCurrency(summary.totalRefundAmt) }}</div>
        </div>
      </div>
      <div class="summary-row">
        <div class="summary-item">
          <div class="summary-label">收款笔数</div>
          <div class="summary-value">{{ summary.transNumber }} 笔</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">手续费</div>
          <div class="summary-value">-¥{{ formatCurrency(summary.totalFeeAmt) }}</div>
        </div>
      </div>
    </div>

    <!-- Transaction list -->
    <div class="transaction-list">
      <div class="transaction-items">
        <div v-for="(transaction, index) in displayTransactions" :key="transaction.sysSeqId || index"
          class="transaction-item">
          <div class="transaction-left">
            <img :src="getPayTypeIcon(transaction.payType)" :alt="formatPaymentMethod(transaction.payType)"
              class="payment-icon" />
            <div class="transaction-left-content">
              <div class="store-name">{{ transaction.shopName || '未知门店' }}</div>
              <div class="transaction-time">{{ formatTransactionTime(transaction.transTime) }}</div>
            </div>
          </div>
          <div class="transaction-right">
            <div class="transaction-amount" :class="getAmountClass(transaction.transStat)">
              {{ formatTransactionAmount(transaction.transAmt, transaction.transStat) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Show more button -->
    <div class="more-trans-btn" @click="moreTransClick()" v-if="hasMoreTransactions">
      查看更多 ({{ remainingCount }})
    </div>
  </div>
</template>

<script>
export default {
  props: {
    transInfos: {
      // 交易列表
      type: Array,
      default() {
        return []
      },
    },
    transDate: {
      type: String,
      default: '', //格式：YYYYMMDD
    },
    totalAmount: {
      // 实收金额(元)
      type: String,
      default: '',
    },
    totalRefundAmt: {
      //退款金额(元)
      type: String,
      default: '',
    },
    transNumber: {
      //收款笔数
      type: String,
      default: '',
    },
    totalFeeAmt: {
      //手续费金额(元)
      type: String,
      default: '',
    },
  },
  data() {
    return {
      maxDisplayCount: 3, // 最多显示条数
    }
  },
  computed: {
    formatDateRange() {
      if (this.transDate) {
        return this.formatDisplayDate(this.transDate)
      }
      return '交易列表'
    },

    displayTransactions() {
      return this.transInfos.slice(0, this.maxDisplayCount)
    },

    hasMoreTransactions() {
      return this.transInfos.length > this.maxDisplayCount
    },

    remainingCount() {
      return Math.max(0, this.transInfos.length - this.maxDisplayCount)
    },

    totalCount() {
      return this.transInfos.length
    },

    summary() {
      let totalAmount = parseFloat(this.totalAmount || 0)
      let totalRefundAmt = parseFloat(this.totalRefundAmt || 0)
      let transNumber = parseInt(this.transNumber || 0)
      let totalFeeAmt = parseFloat(this.totalFeeAmt || 0)

      return {
        totalAmount,
        totalRefundAmt,
        transNumber,
        totalFeeAmt,
      }
    },
  },
  methods: {
    formatDisplayDate(dateStr) {
      if (dateStr && dateStr.length === 8) {
        return `${dateStr.substring(0, 4)}/${dateStr.substring(4, 6)}/${dateStr.substring(6, 8)}`
      }
      return dateStr
    },

    formatCurrency(amount) {
      const num = parseFloat(amount || 0)
      return num.toFixed(2)
    },

    formatTransactionTime(transTime) {
      if (!transTime) return '未知时间'

      // Handle datetime format: YYYY-MM-DD HH:MM:SS
      if (transTime.includes('-') && transTime.includes(':')) {
        const [datePart, timePart] = transTime.split(' ')
        const [year, month, day] = datePart.split('-')
        const [hour, minute] = timePart.split(':')
        return `${month}/${day} ${hour}:${minute}`
      }

      // Handle format: YYYYMMDDHHMMSS
      if (transTime.length === 14) {
        const year = transTime.substring(0, 4)
        const month = transTime.substring(4, 6)
        const day = transTime.substring(6, 8)
        const hour = transTime.substring(8, 10)
        const minute = transTime.substring(10, 12)
        return `${month}/${day} ${hour}:${minute}`
      }

      return transTime
    },

    formatPaymentMethod(payType) {
      const payTypeMap = {
        U: '银联云闪付',
        W: '微信',
        JDBT: '京东白条',
        OTHER: '其他',
        CONSUME: 'POS刷卡',
        A: '支付宝',
      }
      return payTypeMap[payType] || payType || '未知'
    },

    getPayTypeIcon(payType) {
      // 根据 payType 返回对应的图标路径
      const iconMap = {
        A: new URL('../assets/pay_type_ali.png', import.meta.url).href, // 支付宝
        W: new URL('../assets/pay_type_wx.png', import.meta.url).href, // 微信
        U: new URL('../assets/pay_type_union.png', import.meta.url).href, // 银联云闪付
        JDBT: new URL('../assets/pay_type_jd.png', import.meta.url).href, // 京东白条
      }

      // 如果是 A/W/U/JDBT 中的一个，返回对应图标，否则使用默认卡片图标
      return iconMap[payType] || new URL('../assets/pay_type_card.png', import.meta.url).href
    },

    formatTransactionAmount(amount, transStat) {
      const num = parseFloat(amount || 0)
      if (transStat === 'R') {
        // 退款
        return `-¥${num.toFixed(2)}`
      }
      return `+¥${num.toFixed(2)}`
    },

    getAmountClass(transStat) {
      return transStat === 'R' ? 'negative' : 'positive'
    },

    // 查看更多交易
    moreTransClick() {
      this.maxDisplayCount += 10
      console.log('查看更多交易', {
        current: this.maxDisplayCount,
        total: this.transInfos.length,
      })
    },
  },
  mounted() {
    console.log('transInfos:', this.transInfos)
  },
}
</script>

<style lang="scss" scoped>
.trans-list-card-container {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
  margin: 16px 0;

  .card-header {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    padding: 16px 20px;

    .date-range {
      font-size: 16px;
      font-weight: 600;
      color: white;
      margin-bottom: 4px;
    }

    .total-count {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
    }
  }

  .summary-section {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    .summary-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
    }

    .summary-item {
      flex: 1;
      text-align: center;
      padding: 0 8px;

      .summary-label {
        font-size: 12px;
        color: #6c757d;
        margin-bottom: 4px;
        font-weight: 500;
      }

      .summary-value {
        font-size: 16px;
        font-weight: 600;
        color: #495057;

        &.positive {
          color: #28a745;
        }

        &.negative {
          color: #dc3545;
        }
      }
    }
  }

  .transaction-list {
    .transaction-items {
      max-height: 400px;
      overflow-y: auto;
    }

    .transaction-item {
      display: flex;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid #f1f3f4;
      transition: background-color 0.2s ease;

      .transaction-left {
        display: flex;

        .payment-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          object-fit: cover;
          flex-shrink: 0;
        }

        .transaction-left-content {
          margin-left: 16px;

          .store-name {
            font-size: 14px;
            font-weight: 600;
            color: #343a40;
          }

          .transaction-time {
            font-size: 12px;
            color: #6c757d;
          }
        }
      }

      .transaction-right {
        text-align: right;

        .transaction-amount {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 2px;

          &.positive {
            color: #28a745;
          }

          &.negative {
            color: #dc3545;
          }
        }

        .transaction-status {
          font-size: 11px;
          color: #6c757d;
          font-weight: 500;
        }
      }
    }
  }

  .more-trans-btn {
    padding: 12px 20px;
    font-size: 14px;
    color: #6a11cb;
    text-align: center;
    cursor: pointer;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    transition: all 0.2s ease;
    font-weight: 500;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .trans-list-card-container {
    margin: 12px 8px;
    border-radius: 12px;

    .card-header {
      padding: 14px 16px;
    }

    .summary-section {
      padding: 16px;

      .summary-row {
        margin-bottom: 12px;
      }

      .summary-item {
        .summary-value {
          font-size: 14px;
        }
      }
    }

    .transaction-list {
      .transaction-item {
        padding: 12px 16px;

        .transaction-left {
          .store-name {
            font-size: 13px;
          }
        }

        .transaction-right {
          .transaction-amount {
            font-size: 14px;
          }
        }
      }
    }

    .more-trans-btn {
      padding: 10px 16px;
    }
  }
}
</style>
