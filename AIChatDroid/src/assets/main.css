@import './base.css';

/* Custom styles for mobile-first design */
#app {
  min-height: 100vh;
  background-color: #f9fafb;
}

/* Custom component styles */
.chat-container {
  width: 100%;
  max-width: 56rem;
  margin: 0 auto;
  padding: 1rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.mobile-optimized {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}
  
.app-header {
  background-color: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid #e5e7eb;
  padding: 0.75rem 1rem;
}

.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 9999px;
  height: 2rem;
  width: 2rem;
  border-bottom: 2px solid #2563eb;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Deep Chat custom styles */
.deep-chat-mobile {
  width: 100%;
  height: 100%;
  border: 0;
  border-radius: 0;
}

.deep-chat-desktop {
  width: 100%;
  max-width: 42rem;
  margin: 0 auto;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
