lockfileVersion: 5.4

specifiers:
  '@microsoft/fetch-event-source': ^2.0.1
  '@tailwindcss/typography': ^0.5.16
  '@tsconfig/node22': ^22.0.2
  '@types/node': ^22.16.5
  '@unocss/preset-attributify': ^66.5.1
  '@unocss/preset-icons': ^66.5.1
  '@unocss/preset-typography': ^66.5.1
  '@unocss/preset-uno': ^66.5.1
  '@unocss/preset-web-fonts': ^66.5.1
  '@vitejs/plugin-vue': ^5.2.4
  '@vue/eslint-config-prettier': ^10.2.0
  '@vue/eslint-config-typescript': ^14.6.0
  '@vue/tsconfig': ^0.7.0
  '@vue/web-component-wrapper': ^1.3.0
  autoprefixer: ^10.4.21
  deep-chat: ^2.2.2
  eslint: ^9.31.0
  eslint-plugin-vue: ~10.3.0
  jiti: ^2.4.2
  npm-run-all2: ^8.0.4
  pinia: ^3.0.3
  postcss: ^8.5.6
  prettier: 3.6.2
  sass: ^1.93.1
  typescript: ~5.8.0
  unocss: ^66.5.1
  vite: ^6.0.0
  vite-plugin-vue-devtools: ^8.0.0
  vue: ^3.5.18
  vue-router: ^4.5.1
  vue-tsc: ^3.0.4
  vue-web-component-wrapper: ^1.7.7

dependencies:
  '@microsoft/fetch-event-source': registry.npmjs.org/@microsoft/fetch-event-source/2.0.1
  '@vue/web-component-wrapper': registry.npmjs.org/@vue/web-component-wrapper/1.3.0
  deep-chat: registry.npmjs.org/deep-chat/2.2.2
  pinia: registry.npmjs.org/pinia/3.0.3_5w2wwtazrjreqxulnlwvyyqa5u
  vue: registry.npmjs.org/vue/3.5.22_typescript@5.8.3
  vue-router: registry.npmjs.org/vue-router/4.5.1_vue@3.5.22
  vue-web-component-wrapper: registry.npmjs.org/vue-web-component-wrapper/1.7.7

devDependencies:
  '@tailwindcss/typography': registry.npmjs.org/@tailwindcss/typography/0.5.19
  '@tsconfig/node22': registry.npmjs.org/@tsconfig/node22/22.0.2
  '@types/node': registry.npmjs.org/@types/node/22.18.6
  '@unocss/preset-attributify': registry.npmjs.org/@unocss/preset-attributify/66.5.2
  '@unocss/preset-icons': registry.npmjs.org/@unocss/preset-icons/66.5.2
  '@unocss/preset-typography': registry.npmjs.org/@unocss/preset-typography/66.5.2
  '@unocss/preset-uno': registry.npmjs.org/@unocss/preset-uno/66.5.2
  '@unocss/preset-web-fonts': registry.npmjs.org/@unocss/preset-web-fonts/66.5.2
  '@vitejs/plugin-vue': registry.npmjs.org/@vitejs/plugin-vue/5.2.4_vite@6.3.6+vue@3.5.22
  '@vue/eslint-config-prettier': registry.npmjs.org/@vue/eslint-config-prettier/10.2.0_m6uex47n6d4n3zhzjzmrk6xdnm
  '@vue/eslint-config-typescript': registry.npmjs.org/@vue/eslint-config-typescript/14.6.0_bak4comehp2dohjfcynw7odnqi
  '@vue/tsconfig': registry.npmjs.org/@vue/tsconfig/0.7.0_5w2wwtazrjreqxulnlwvyyqa5u
  autoprefixer: registry.npmjs.org/autoprefixer/10.4.21_postcss@8.5.6
  eslint: registry.npmjs.org/eslint/9.36.0_jiti@2.6.0
  eslint-plugin-vue: registry.npmjs.org/eslint-plugin-vue/10.3.0_eslint@9.36.0
  jiti: registry.npmjs.org/jiti/2.6.0
  npm-run-all2: registry.npmjs.org/npm-run-all2/8.0.4
  postcss: registry.npmjs.org/postcss/8.5.6
  prettier: registry.npmjs.org/prettier/3.6.2
  sass: registry.npmjs.org/sass/1.93.2
  typescript: registry.npmjs.org/typescript/5.8.3
  unocss: registry.npmjs.org/unocss/66.5.2_postcss@8.5.6+vite@6.3.6
  vite: registry.npmjs.org/vite/6.3.6_nnwfsygotcqvs65kz7hc73f7s4
  vite-plugin-vue-devtools: registry.npmjs.org/vite-plugin-vue-devtools/8.0.2_vite@6.3.6+vue@3.5.22
  vue-tsc: registry.npmjs.org/vue-tsc/3.0.8_typescript@5.8.3

packages:

  registry.npmjs.org/@antfu/install-pkg/1.1.0:
    resolution: {integrity: sha512-MGQsmw10ZyI+EJo45CdSER4zEb+p31LpDAFp2Z3gkSd1yqVZGi0Ebx++YTEMonJy4oChEMLsxZ64j8FH6sSqtQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@antfu/install-pkg/-/install-pkg-1.1.0.tgz}
    name: '@antfu/install-pkg'
    version: 1.1.0
    dependencies:
      package-manager-detector: registry.npmjs.org/package-manager-detector/1.3.0
      tinyexec: registry.npmjs.org/tinyexec/1.0.1
    dev: true

  registry.npmjs.org/@antfu/utils/9.2.1:
    resolution: {integrity: sha512-TMilPqXyii1AsiEii6l6ubRzbo76p6oshUSYPaKsmXDavyMLqjzVDkcp3pHp5ELMUNJHATcEOGxKTTsX9yYhGg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@antfu/utils/-/utils-9.2.1.tgz}
    name: '@antfu/utils'
    version: 9.2.1
    dev: true

  registry.npmjs.org/@babel/code-frame/7.27.1:
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz}
    name: '@babel/code-frame'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': registry.npmjs.org/@babel/helper-validator-identifier/7.27.1
      js-tokens: registry.npmjs.org/js-tokens/4.0.0
      picocolors: registry.npmjs.org/picocolors/1.1.1
    dev: true

  registry.npmjs.org/@babel/compat-data/7.28.4:
    resolution: {integrity: sha512-YsmSKC29MJwf0gF8Rjjrg5LQCmyh+j/nD8/eP7f+BeoQTKYqs9RoWbjGOdy0+1Ekr68RJZMUOPVQaQisnIo4Rw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.4.tgz}
    name: '@babel/compat-data'
    version: 7.28.4
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmjs.org/@babel/core/7.28.4:
    resolution: {integrity: sha512-2BCOP7TN8M+gVDj7/ht3hsaO/B/n5oDbiAyyvnRlNOs+u1o+JWNYTQrmpuNp1/Wq2gcFrI01JAW+paEKDMx/CA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/core/-/core-7.28.4.tgz}
    name: '@babel/core'
    version: 7.28.4
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': registry.npmjs.org/@babel/code-frame/7.27.1
      '@babel/generator': registry.npmjs.org/@babel/generator/7.28.3
      '@babel/helper-compilation-targets': registry.npmjs.org/@babel/helper-compilation-targets/7.27.2
      '@babel/helper-module-transforms': registry.npmjs.org/@babel/helper-module-transforms/7.28.3_@babel+core@7.28.4
      '@babel/helpers': registry.npmjs.org/@babel/helpers/7.28.4
      '@babel/parser': registry.npmjs.org/@babel/parser/7.28.4
      '@babel/template': registry.npmjs.org/@babel/template/7.27.2
      '@babel/traverse': registry.npmjs.org/@babel/traverse/7.28.4
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4
      '@jridgewell/remapping': registry.npmjs.org/@jridgewell/remapping/2.3.5
      convert-source-map: registry.npmjs.org/convert-source-map/2.0.0
      debug: registry.npmjs.org/debug/4.4.3
      gensync: registry.npmjs.org/gensync/1.0.0-beta.2
      json5: registry.npmjs.org/json5/2.2.3
      semver: registry.npmjs.org/semver/6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/generator/7.28.3:
    resolution: {integrity: sha512-3lSpxGgvnmZznmBkCRnVREPUFJv2wrv9iAoFDvADJc0ypmdOxdUtcLeBgBJ6zE0PMeTKnxeQzyk0xTBq4Ep7zw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/generator/-/generator-7.28.3.tgz}
    name: '@babel/generator'
    version: 7.28.3
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/parser': registry.npmjs.org/@babel/parser/7.28.4
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4
      '@jridgewell/gen-mapping': registry.npmjs.org/@jridgewell/gen-mapping/0.3.13
      '@jridgewell/trace-mapping': registry.npmjs.org/@jridgewell/trace-mapping/0.3.31
      jsesc: registry.npmjs.org/jsesc/3.1.0
    dev: true

  registry.npmjs.org/@babel/helper-annotate-as-pure/7.27.3:
    resolution: {integrity: sha512-fXSwMQqitTGeHLBC08Eq5yXz2m37E4pJX1qAU1+2cNedz/ifv/bVXft90VeSav5nFO61EcNgwr0aJxbyPaWBPg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz}
    name: '@babel/helper-annotate-as-pure'
    version: 7.27.3
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4
    dev: true

  registry.npmjs.org/@babel/helper-compilation-targets/7.27.2:
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz}
    name: '@babel/helper-compilation-targets'
    version: 7.27.2
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/compat-data': registry.npmjs.org/@babel/compat-data/7.28.4
      '@babel/helper-validator-option': registry.npmjs.org/@babel/helper-validator-option/7.27.1
      browserslist: registry.npmjs.org/browserslist/4.26.2
      lru-cache: registry.npmjs.org/lru-cache/5.1.1
      semver: registry.npmjs.org/semver/6.3.1
    dev: true

  registry.npmjs.org/@babel/helper-create-class-features-plugin/7.28.3_@babel+core@7.28.4:
    resolution: {integrity: sha512-V9f6ZFIYSLNEbuGA/92uOvYsGCJNsuA8ESZ4ldc09bWk/j8H8TKiPw8Mk1eG6olpnO0ALHJmYfZvF4MEE4gajg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.28.3.tgz}
    id: registry.npmjs.org/@babel/helper-create-class-features-plugin/7.28.3
    name: '@babel/helper-create-class-features-plugin'
    version: 7.28.3
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core/7.28.4
      '@babel/helper-annotate-as-pure': registry.npmjs.org/@babel/helper-annotate-as-pure/7.27.3
      '@babel/helper-member-expression-to-functions': registry.npmjs.org/@babel/helper-member-expression-to-functions/7.27.1
      '@babel/helper-optimise-call-expression': registry.npmjs.org/@babel/helper-optimise-call-expression/7.27.1
      '@babel/helper-replace-supers': registry.npmjs.org/@babel/helper-replace-supers/7.27.1_@babel+core@7.28.4
      '@babel/helper-skip-transparent-expression-wrappers': registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/7.27.1
      '@babel/traverse': registry.npmjs.org/@babel/traverse/7.28.4
      semver: registry.npmjs.org/semver/6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/helper-globals/7.28.0:
    resolution: {integrity: sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz}
    name: '@babel/helper-globals'
    version: 7.28.0
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmjs.org/@babel/helper-member-expression-to-functions/7.27.1:
    resolution: {integrity: sha512-E5chM8eWjTp/aNoVpcbfM7mLxu9XGLWYise2eBKGQomAk/Mb4XoxyqXTZbuTohbsl8EKqdlMhnDI2CCLfcs9wA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.27.1.tgz}
    name: '@babel/helper-member-expression-to-functions'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': registry.npmjs.org/@babel/traverse/7.28.4
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/helper-module-imports/7.27.1:
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz}
    name: '@babel/helper-module-imports'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': registry.npmjs.org/@babel/traverse/7.28.4
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/helper-module-transforms/7.28.3_@babel+core@7.28.4:
    resolution: {integrity: sha512-gytXUbs8k2sXS9PnQptz5o0QnpLL51SwASIORY6XaBKF88nsOT0Zw9szLqlSGQDP/4TljBAD5y98p2U1fqkdsw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.28.3.tgz}
    id: registry.npmjs.org/@babel/helper-module-transforms/7.28.3
    name: '@babel/helper-module-transforms'
    version: 7.28.3
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core/7.28.4
      '@babel/helper-module-imports': registry.npmjs.org/@babel/helper-module-imports/7.27.1
      '@babel/helper-validator-identifier': registry.npmjs.org/@babel/helper-validator-identifier/7.27.1
      '@babel/traverse': registry.npmjs.org/@babel/traverse/7.28.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/helper-optimise-call-expression/7.27.1:
    resolution: {integrity: sha512-URMGH08NzYFhubNSGJrpUEphGKQwMQYBySzat5cAByY1/YgIRkULnIy3tAMeszlL/so2HbeilYloUmSpd7GdVw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.27.1.tgz}
    name: '@babel/helper-optimise-call-expression'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4
    dev: true

  registry.npmjs.org/@babel/helper-plugin-utils/7.27.1:
    resolution: {integrity: sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz}
    name: '@babel/helper-plugin-utils'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmjs.org/@babel/helper-replace-supers/7.27.1_@babel+core@7.28.4:
    resolution: {integrity: sha512-7EHz6qDZc8RYS5ElPoShMheWvEgERonFCs7IAonWLLUTXW59DP14bCZt89/GKyreYn8g3S83m21FelHKbeDCKA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.27.1.tgz}
    id: registry.npmjs.org/@babel/helper-replace-supers/7.27.1
    name: '@babel/helper-replace-supers'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core/7.28.4
      '@babel/helper-member-expression-to-functions': registry.npmjs.org/@babel/helper-member-expression-to-functions/7.27.1
      '@babel/helper-optimise-call-expression': registry.npmjs.org/@babel/helper-optimise-call-expression/7.27.1
      '@babel/traverse': registry.npmjs.org/@babel/traverse/7.28.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/7.27.1:
    resolution: {integrity: sha512-Tub4ZKEXqbPjXgWLl2+3JpQAYBJ8+ikpQ2Ocj/q/r0LwE3UhENh7EUabyHjz2kCEsrRY83ew2DQdHluuiDQFzg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.27.1.tgz}
    name: '@babel/helper-skip-transparent-expression-wrappers'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': registry.npmjs.org/@babel/traverse/7.28.4
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/helper-string-parser/7.27.1:
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz}
    name: '@babel/helper-string-parser'
    version: 7.27.1
    engines: {node: '>=6.9.0'}

  registry.npmjs.org/@babel/helper-validator-identifier/7.27.1:
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz}
    name: '@babel/helper-validator-identifier'
    version: 7.27.1
    engines: {node: '>=6.9.0'}

  registry.npmjs.org/@babel/helper-validator-option/7.27.1:
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz}
    name: '@babel/helper-validator-option'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmjs.org/@babel/helpers/7.28.4:
    resolution: {integrity: sha512-HFN59MmQXGHVyYadKLVumYsA9dBFun/ldYxipEjzA4196jpLZd8UjEEBLkbEkvfYreDqJhZxYAWFPtrfhNpj4w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/helpers/-/helpers-7.28.4.tgz}
    name: '@babel/helpers'
    version: 7.28.4
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': registry.npmjs.org/@babel/template/7.27.2
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4
    dev: true

  registry.npmjs.org/@babel/parser/7.27.7:
    resolution: {integrity: sha512-qnzXzDXdr/po3bOTbTIQZ7+TxNKxpkN5IifVLXS+r7qwynkZfPyjZfE7hCXbo7IoO9TNcSyibgONsf2HauUd3Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/parser/-/parser-7.27.7.tgz}
    name: '@babel/parser'
    version: 7.27.7
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4
    dev: true

  registry.npmjs.org/@babel/parser/7.28.4:
    resolution: {integrity: sha512-yZbBqeM6TkpP9du/I2pUZnJsRMGGvOuIrhjzC1AwHwW+6he4mni6Bp/m8ijn0iOuZuPI2BfkCoSRunpyjnrQKg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/parser/-/parser-7.28.4.tgz}
    name: '@babel/parser'
    version: 7.28.4
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4

  registry.npmjs.org/@babel/plugin-proposal-decorators/7.28.0_@babel+core@7.28.4:
    resolution: {integrity: sha512-zOiZqvANjWDUaUS9xMxbMcK/Zccztbe/6ikvUXaG9nsPH3w6qh5UaPGAnirI/WhIbZ8m3OHU0ReyPrknG+ZKeg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.28.0.tgz}
    id: registry.npmjs.org/@babel/plugin-proposal-decorators/7.28.0
    name: '@babel/plugin-proposal-decorators'
    version: 7.28.0
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core/7.28.4
      '@babel/helper-create-class-features-plugin': registry.npmjs.org/@babel/helper-create-class-features-plugin/7.28.3_@babel+core@7.28.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils/7.27.1
      '@babel/plugin-syntax-decorators': registry.npmjs.org/@babel/plugin-syntax-decorators/7.27.1_@babel+core@7.28.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-decorators/7.27.1_@babel+core@7.28.4:
    resolution: {integrity: sha512-YMq8Z87Lhl8EGkmb0MwYkt36QnxC+fzCgrl66ereamPlYToRpIk5nUjKUY3QKLWq8mwUB1BgbeXcTJhZOCDg5A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.27.1.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-decorators/7.27.1
    name: '@babel/plugin-syntax-decorators'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core/7.28.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils/7.27.1
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-import-attributes/7.27.1_@babel+core@7.28.4:
    resolution: {integrity: sha512-oFT0FrKHgF53f4vOsZGi2Hh3I35PfSmVs4IBFLFj4dnafP+hIWDLg3VyKmUHfLoLHlyxY4C7DGtmHuJgn+IGww==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-import-attributes/7.27.1
    name: '@babel/plugin-syntax-import-attributes'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core/7.28.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils/7.27.1
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-import-meta/7.10.4_@babel+core@7.28.4:
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-import-meta/7.10.4
    name: '@babel/plugin-syntax-import-meta'
    version: 7.10.4
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core/7.28.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils/7.27.1
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-jsx/7.27.1_@babel+core@7.28.4:
    resolution: {integrity: sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-jsx/7.27.1
    name: '@babel/plugin-syntax-jsx'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core/7.28.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils/7.27.1
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-typescript/7.27.1_@babel+core@7.28.4:
    resolution: {integrity: sha512-xfYCBMxveHrRMnAWl1ZlPXOZjzkN82THFvLhQhFXFt81Z5HnN+EtUkZhv/zcKpmT3fzmWZB0ywiBrbC3vogbwQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-typescript/7.27.1
    name: '@babel/plugin-syntax-typescript'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core/7.28.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils/7.27.1
    dev: true

  registry.npmjs.org/@babel/plugin-transform-typescript/7.28.0_@babel+core@7.28.4:
    resolution: {integrity: sha512-4AEiDEBPIZvLQaWlc9liCavE0xRM0dNca41WtBeM3jgFptfUOSG9z0uteLhq6+3rq+WB6jIvUwKDTpXEHPJ2Vg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.28.0.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-typescript/7.28.0
    name: '@babel/plugin-transform-typescript'
    version: 7.28.0
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core/7.28.4
      '@babel/helper-annotate-as-pure': registry.npmjs.org/@babel/helper-annotate-as-pure/7.27.3
      '@babel/helper-create-class-features-plugin': registry.npmjs.org/@babel/helper-create-class-features-plugin/7.28.3_@babel+core@7.28.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils/7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/7.27.1
      '@babel/plugin-syntax-typescript': registry.npmjs.org/@babel/plugin-syntax-typescript/7.27.1_@babel+core@7.28.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/template/7.27.2:
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz}
    name: '@babel/template'
    version: 7.27.2
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': registry.npmjs.org/@babel/code-frame/7.27.1
      '@babel/parser': registry.npmjs.org/@babel/parser/7.27.7
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4
    dev: true

  registry.npmjs.org/@babel/traverse/7.27.7:
    resolution: {integrity: sha512-X6ZlfR/O/s5EQ/SnUSLzr+6kGnkg8HXGMzpgsMsrJVcfDtH1vIp6ctCN4eZ1LS5c0+te5Cb6Y514fASjMRJ1nw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.7.tgz}
    name: '@babel/traverse'
    version: 7.27.7
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': registry.npmjs.org/@babel/code-frame/7.27.1
      '@babel/generator': registry.npmjs.org/@babel/generator/7.28.3
      '@babel/parser': registry.npmjs.org/@babel/parser/7.27.7
      '@babel/template': registry.npmjs.org/@babel/template/7.27.2
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4
      debug: registry.npmjs.org/debug/4.4.3
      globals: registry.npmjs.org/globals/11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/traverse/7.28.4:
    resolution: {integrity: sha512-YEzuboP2qvQavAcjgQNVgsvHIDv6ZpwXvcvjmyySP2DIMuByS/6ioU5G9pYrWHM6T2YDfc7xga9iNzYOs12CFQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.4.tgz}
    name: '@babel/traverse'
    version: 7.28.4
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': registry.npmjs.org/@babel/code-frame/7.27.1
      '@babel/generator': registry.npmjs.org/@babel/generator/7.28.3
      '@babel/helper-globals': registry.npmjs.org/@babel/helper-globals/7.28.0
      '@babel/parser': registry.npmjs.org/@babel/parser/7.28.4
      '@babel/template': registry.npmjs.org/@babel/template/7.27.2
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4
      debug: registry.npmjs.org/debug/4.4.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/types/7.28.4:
    resolution: {integrity: sha512-bkFqkLhh3pMBUQQkpVgWDWq/lqzc2678eUyDlTBhRqhCHFguYYGM0Efga7tYk4TogG/3x0EEl66/OQ+WGbWB/Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@babel/types/-/types-7.28.4.tgz}
    name: '@babel/types'
    version: 7.28.4
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': registry.npmjs.org/@babel/helper-string-parser/7.27.1
      '@babel/helper-validator-identifier': registry.npmjs.org/@babel/helper-validator-identifier/7.27.1

  registry.npmjs.org/@esbuild/aix-ppc64/0.25.10:
    resolution: {integrity: sha512-0NFWnA+7l41irNuaSVlLfgNT12caWJVLzp5eAVhZ0z1qpxbockccEt3s+149rE64VUI3Ml2zt8Nv5JVc4QXTsw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.25.10.tgz}
    name: '@esbuild/aix-ppc64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/android-arm/0.25.10:
    resolution: {integrity: sha512-dQAxF1dW1C3zpeCDc5KqIYuZ1tgAdRXNoZP7vkBIRtKZPYe2xVr/d3SkirklCHudW1B45tGiUlz2pUWDfbDD4w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.10.tgz}
    name: '@esbuild/android-arm'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/android-arm64/0.25.10:
    resolution: {integrity: sha512-LSQa7eDahypv/VO6WKohZGPSJDq5OVOo3UoFR1E4t4Gj1W7zEQMUhI+lo81H+DtB+kP+tDgBp+M4oNCwp6kffg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.25.10.tgz}
    name: '@esbuild/android-arm64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/android-x64/0.25.10:
    resolution: {integrity: sha512-MiC9CWdPrfhibcXwr39p9ha1x0lZJ9KaVfvzA0Wxwz9ETX4v5CHfF09bx935nHlhi+MxhA63dKRRQLiVgSUtEg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.25.10.tgz}
    name: '@esbuild/android-x64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/darwin-arm64/0.25.10:
    resolution: {integrity: sha512-JC74bdXcQEpW9KkV326WpZZjLguSZ3DfS8wrrvPMHgQOIEIG/sPXEN/V8IssoJhbefLRcRqw6RQH2NnpdprtMA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.25.10.tgz}
    name: '@esbuild/darwin-arm64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/darwin-x64/0.25.10:
    resolution: {integrity: sha512-tguWg1olF6DGqzws97pKZ8G2L7Ig1vjDmGTwcTuYHbuU6TTjJe5FXbgs5C1BBzHbJ2bo1m3WkQDbWO2PvamRcg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.25.10.tgz}
    name: '@esbuild/darwin-x64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/freebsd-arm64/0.25.10:
    resolution: {integrity: sha512-3ZioSQSg1HT2N05YxeJWYR+Libe3bREVSdWhEEgExWaDtyFbbXWb49QgPvFH8u03vUPX10JhJPcz7s9t9+boWg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.10.tgz}
    name: '@esbuild/freebsd-arm64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/freebsd-x64/0.25.10:
    resolution: {integrity: sha512-LLgJfHJk014Aa4anGDbh8bmI5Lk+QidDmGzuC2D+vP7mv/GeSN+H39zOf7pN5N8p059FcOfs2bVlrRr4SK9WxA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.10.tgz}
    name: '@esbuild/freebsd-x64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/linux-arm/0.25.10:
    resolution: {integrity: sha512-oR31GtBTFYCqEBALI9r6WxoU/ZofZl962pouZRTEYECvNF/dtXKku8YXcJkhgK/beU+zedXfIzHijSRapJY3vg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.10.tgz}
    name: '@esbuild/linux-arm'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/linux-arm64/0.25.10:
    resolution: {integrity: sha512-5luJWN6YKBsawd5f9i4+c+geYiVEw20FVW5x0v1kEMWNq8UctFjDiMATBxLvmmHA4bf7F6hTRaJgtghFr9iziQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.25.10.tgz}
    name: '@esbuild/linux-arm64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/linux-ia32/0.25.10:
    resolution: {integrity: sha512-NrSCx2Kim3EnnWgS4Txn0QGt0Xipoumb6z6sUtl5bOEZIVKhzfyp/Lyw4C1DIYvzeW/5mWYPBFJU3a/8Yr75DQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.25.10.tgz}
    name: '@esbuild/linux-ia32'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/linux-loong64/0.25.10:
    resolution: {integrity: sha512-xoSphrd4AZda8+rUDDfD9J6FUMjrkTz8itpTITM4/xgerAZZcFW7Dv+sun7333IfKxGG8gAq+3NbfEMJfiY+Eg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.25.10.tgz}
    name: '@esbuild/linux-loong64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/linux-mips64el/0.25.10:
    resolution: {integrity: sha512-ab6eiuCwoMmYDyTnyptoKkVS3k8fy/1Uvq7Dj5czXI6DF2GqD2ToInBI0SHOp5/X1BdZ26RKc5+qjQNGRBelRA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.10.tgz}
    name: '@esbuild/linux-mips64el'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/linux-ppc64/0.25.10:
    resolution: {integrity: sha512-NLinzzOgZQsGpsTkEbdJTCanwA5/wozN9dSgEl12haXJBzMTpssebuXR42bthOF3z7zXFWH1AmvWunUCkBE4EA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.25.10.tgz}
    name: '@esbuild/linux-ppc64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/linux-riscv64/0.25.10:
    resolution: {integrity: sha512-FE557XdZDrtX8NMIeA8LBJX3dC2M8VGXwfrQWU7LB5SLOajfJIxmSdyL/gU1m64Zs9CBKvm4UAuBp5aJ8OgnrA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.25.10.tgz}
    name: '@esbuild/linux-riscv64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/linux-s390x/0.25.10:
    resolution: {integrity: sha512-3BBSbgzuB9ajLoVZk0mGu+EHlBwkusRmeNYdqmznmMc9zGASFjSsxgkNsqmXugpPk00gJ0JNKh/97nxmjctdew==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.10.tgz}
    name: '@esbuild/linux-s390x'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/linux-x64/0.25.10:
    resolution: {integrity: sha512-QSX81KhFoZGwenVyPoberggdW1nrQZSvfVDAIUXr3WqLRZGZqWk/P4T8p2SP+de2Sr5HPcvjhcJzEiulKgnxtA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.25.10.tgz}
    name: '@esbuild/linux-x64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/netbsd-arm64/0.25.10:
    resolution: {integrity: sha512-AKQM3gfYfSW8XRk8DdMCzaLUFB15dTrZfnX8WXQoOUpUBQ+NaAFCP1kPS/ykbbGYz7rxn0WS48/81l9hFl3u4A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.10.tgz}
    name: '@esbuild/netbsd-arm64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/netbsd-x64/0.25.10:
    resolution: {integrity: sha512-7RTytDPGU6fek/hWuN9qQpeGPBZFfB4zZgcz2VK2Z5VpdUxEI8JKYsg3JfO0n/Z1E/6l05n0unDCNc4HnhQGig==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.10.tgz}
    name: '@esbuild/netbsd-x64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/openbsd-arm64/0.25.10:
    resolution: {integrity: sha512-5Se0VM9Wtq797YFn+dLimf2Zx6McttsH2olUBsDml+lm0GOCRVebRWUvDtkY4BWYv/3NgzS8b/UM3jQNh5hYyw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.10.tgz}
    name: '@esbuild/openbsd-arm64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/openbsd-x64/0.25.10:
    resolution: {integrity: sha512-XkA4frq1TLj4bEMB+2HnI0+4RnjbuGZfet2gs/LNs5Hc7D89ZQBHQ0gL2ND6Lzu1+QVkjp3x1gIcPKzRNP8bXw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.25.10.tgz}
    name: '@esbuild/openbsd-x64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/openharmony-arm64/0.25.10:
    resolution: {integrity: sha512-AVTSBhTX8Y/Fz6OmIVBip9tJzZEUcY8WLh7I59+upa5/GPhh2/aM6bvOMQySspnCCHvFi79kMtdJS1w0DXAeag==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/openharmony-arm64/-/openharmony-arm64-0.25.10.tgz}
    name: '@esbuild/openharmony-arm64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openharmony]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/sunos-x64/0.25.10:
    resolution: {integrity: sha512-fswk3XT0Uf2pGJmOpDB7yknqhVkJQkAQOcW/ccVOtfx05LkbWOaRAtn5SaqXypeKQra1QaEa841PgrSL9ubSPQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.10.tgz}
    name: '@esbuild/sunos-x64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/win32-arm64/0.25.10:
    resolution: {integrity: sha512-ah+9b59KDTSfpaCg6VdJoOQvKjI33nTaQr4UluQwW7aEwZQsbMCfTmfEO4VyewOxx4RaDT/xCy9ra2GPWmO7Kw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.10.tgz}
    name: '@esbuild/win32-arm64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/win32-ia32/0.25.10:
    resolution: {integrity: sha512-QHPDbKkrGO8/cz9LKVnJU22HOi4pxZnZhhA2HYHez5Pz4JeffhDjf85E57Oyco163GnzNCVkZK0b/n4Y0UHcSw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.25.10.tgz}
    name: '@esbuild/win32-ia32'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@esbuild/win32-x64/0.25.10:
    resolution: {integrity: sha512-9KpxSVFCu0iK1owoez6aC/s/EdUQLDN3adTxGCqxMVhrPDj6bt5dbrHDXUuq+Bs2vATFBBrQS5vdQ/Ed2P+nbw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.25.10.tgz}
    name: '@esbuild/win32-x64'
    version: 0.25.10
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@eslint-community/eslint-utils/4.9.0_eslint@9.36.0:
    resolution: {integrity: sha512-ayVFHdtZ+hsq1t2Dy24wCmGXGe4q9Gu3smhLYALJrr473ZH27MsnSL+LKUlimp4BWJqMDMLmPpx/Q9R3OAlL4g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.9.0.tgz}
    id: registry.npmjs.org/@eslint-community/eslint-utils/4.9.0
    name: '@eslint-community/eslint-utils'
    version: 4.9.0
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
    dependencies:
      eslint: registry.npmjs.org/eslint/9.36.0_jiti@2.6.0
      eslint-visitor-keys: registry.npmjs.org/eslint-visitor-keys/3.4.3
    dev: true

  registry.npmjs.org/@eslint-community/regexpp/4.12.1:
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz}
    name: '@eslint-community/regexpp'
    version: 4.12.1
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}
    dev: true

  registry.npmjs.org/@eslint/config-array/0.21.0:
    resolution: {integrity: sha512-ENIdc4iLu0d93HeYirvKmrzshzofPw6VkZRKQGe9Nv46ZnWUzcF1xV01dcvEg/1wXUR61OmmlSfyeyO7EvjLxQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@eslint/config-array/-/config-array-0.21.0.tgz}
    name: '@eslint/config-array'
    version: 0.21.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@eslint/object-schema': registry.npmjs.org/@eslint/object-schema/2.1.6
      debug: registry.npmjs.org/debug/4.4.3
      minimatch: registry.npmjs.org/minimatch/3.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@eslint/config-helpers/0.3.1:
    resolution: {integrity: sha512-xR93k9WhrDYpXHORXpxVL5oHj3Era7wo6k/Wd8/IsQNnZUTzkGS29lyn3nAT05v6ltUuTFVCCYDEGfy2Or/sPA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@eslint/config-helpers/-/config-helpers-0.3.1.tgz}
    name: '@eslint/config-helpers'
    version: 0.3.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmjs.org/@eslint/core/0.15.2:
    resolution: {integrity: sha512-78Md3/Rrxh83gCxoUc0EiciuOHsIITzLy53m3d9UyiW8y9Dj2D29FeETqyKA+BRK76tnTp6RXWb3pCay8Oyomg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@eslint/core/-/core-0.15.2.tgz}
    name: '@eslint/core'
    version: 0.15.2
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@types/json-schema': registry.npmjs.org/@types/json-schema/7.0.15
    dev: true

  registry.npmjs.org/@eslint/eslintrc/3.3.1:
    resolution: {integrity: sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.3.1.tgz}
    name: '@eslint/eslintrc'
    version: 3.3.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      ajv: registry.npmjs.org/ajv/6.12.6
      debug: registry.npmjs.org/debug/4.4.3
      espree: registry.npmjs.org/espree/10.4.0
      globals: registry.npmjs.org/globals/14.0.0
      ignore: registry.npmjs.org/ignore/5.3.2
      import-fresh: registry.npmjs.org/import-fresh/3.3.1
      js-yaml: registry.npmjs.org/js-yaml/4.1.0
      minimatch: registry.npmjs.org/minimatch/3.1.2
      strip-json-comments: registry.npmjs.org/strip-json-comments/3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@eslint/js/9.36.0:
    resolution: {integrity: sha512-uhCbYtYynH30iZErszX78U+nR3pJU3RHGQ57NXy5QupD4SBVwDeU8TNBy+MjMngc1UyIW9noKqsRqfjQTBU2dw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@eslint/js/-/js-9.36.0.tgz}
    name: '@eslint/js'
    version: 9.36.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmjs.org/@eslint/object-schema/2.1.6:
    resolution: {integrity: sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.6.tgz}
    name: '@eslint/object-schema'
    version: 2.1.6
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmjs.org/@eslint/plugin-kit/0.3.5:
    resolution: {integrity: sha512-Z5kJ+wU3oA7MMIqVR9tyZRtjYPr4OC004Q4Rw7pgOKUOKkJfZ3O24nz3WYfGRpMDNmcOi3TwQOmgm7B7Tpii0w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.3.5.tgz}
    name: '@eslint/plugin-kit'
    version: 0.3.5
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@eslint/core': registry.npmjs.org/@eslint/core/0.15.2
      levn: registry.npmjs.org/levn/0.4.1
    dev: true

  registry.npmjs.org/@humanfs/core/0.19.1:
    resolution: {integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@humanfs/core/-/core-0.19.1.tgz}
    name: '@humanfs/core'
    version: 0.19.1
    engines: {node: '>=18.18.0'}
    dev: true

  registry.npmjs.org/@humanfs/node/0.16.7:
    resolution: {integrity: sha512-/zUx+yOsIrG4Y43Eh2peDeKCxlRt/gET6aHfaKpuq267qXdYDFViVHfMaLyygZOnl0kGWxFIgsBy8QFuTLUXEQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@humanfs/node/-/node-0.16.7.tgz}
    name: '@humanfs/node'
    version: 0.16.7
    engines: {node: '>=18.18.0'}
    dependencies:
      '@humanfs/core': registry.npmjs.org/@humanfs/core/0.19.1
      '@humanwhocodes/retry': registry.npmjs.org/@humanwhocodes/retry/0.4.3
    dev: true

  registry.npmjs.org/@humanwhocodes/module-importer/1.0.1:
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz}
    name: '@humanwhocodes/module-importer'
    version: 1.0.1
    engines: {node: '>=12.22'}
    dev: true

  registry.npmjs.org/@humanwhocodes/retry/0.4.3:
    resolution: {integrity: sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.3.tgz}
    name: '@humanwhocodes/retry'
    version: 0.4.3
    engines: {node: '>=18.18'}
    dev: true

  registry.npmjs.org/@iconify/types/2.0.0:
    resolution: {integrity: sha512-+wluvCrRhXrhyOmRDJ3q8mux9JkKy5SJ/v8ol2tu4FVjyYvtEzkc/3pK15ET6RKg4b4w4BmTk1+gsCUhf21Ykg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@iconify/types/-/types-2.0.0.tgz}
    name: '@iconify/types'
    version: 2.0.0
    dev: true

  registry.npmjs.org/@iconify/utils/3.0.2:
    resolution: {integrity: sha512-EfJS0rLfVuRuJRn4psJHtK2A9TqVnkxPpHY6lYHiB9+8eSuudsxbwMiavocG45ujOo6FJ+CIRlRnlOGinzkaGQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@iconify/utils/-/utils-3.0.2.tgz}
    name: '@iconify/utils'
    version: 3.0.2
    dependencies:
      '@antfu/install-pkg': registry.npmjs.org/@antfu/install-pkg/1.1.0
      '@antfu/utils': registry.npmjs.org/@antfu/utils/9.2.1
      '@iconify/types': registry.npmjs.org/@iconify/types/2.0.0
      debug: registry.npmjs.org/debug/4.4.3
      globals: registry.npmjs.org/globals/15.15.0
      kolorist: registry.npmjs.org/kolorist/1.8.0
      local-pkg: registry.npmjs.org/local-pkg/1.1.2
      mlly: registry.npmjs.org/mlly/1.8.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@jridgewell/gen-mapping/0.3.13:
    resolution: {integrity: sha512-2kkt/7niJ6MgEPxF0bYdQ6etZaA+fQvDcLKckhy1yIQOzaoKjBBjSj63/aLVjYE3qhRt5dvM+uUyfCg6UKCBbA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.13.tgz}
    name: '@jridgewell/gen-mapping'
    version: 0.3.13
    dependencies:
      '@jridgewell/sourcemap-codec': registry.npmjs.org/@jridgewell/sourcemap-codec/1.5.5
      '@jridgewell/trace-mapping': registry.npmjs.org/@jridgewell/trace-mapping/0.3.31
    dev: true

  registry.npmjs.org/@jridgewell/remapping/2.3.5:
    resolution: {integrity: sha512-LI9u/+laYG4Ds1TDKSJW2YPrIlcVYOwi2fUC6xB43lueCjgxV4lffOCZCtYFiH6TNOX+tQKXx97T4IKHbhyHEQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@jridgewell/remapping/-/remapping-2.3.5.tgz}
    name: '@jridgewell/remapping'
    version: 2.3.5
    dependencies:
      '@jridgewell/gen-mapping': registry.npmjs.org/@jridgewell/gen-mapping/0.3.13
      '@jridgewell/trace-mapping': registry.npmjs.org/@jridgewell/trace-mapping/0.3.31
    dev: true

  registry.npmjs.org/@jridgewell/resolve-uri/3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz}
    name: '@jridgewell/resolve-uri'
    version: 3.1.2
    engines: {node: '>=6.0.0'}
    dev: true

  registry.npmjs.org/@jridgewell/sourcemap-codec/1.5.5:
    resolution: {integrity: sha512-cYQ9310grqxueWbl+WuIUIaiUaDcj7WOq5fVhEljNVgRfOUhY9fy2zTvfoqWsnebh8Sl70VScFbICvJnLKB0Og==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.5.tgz}
    name: '@jridgewell/sourcemap-codec'
    version: 1.5.5

  registry.npmjs.org/@jridgewell/trace-mapping/0.3.31:
    resolution: {integrity: sha512-zzNR+SdQSDJzc8joaeP8QQoCQr8NuYx2dIIytl1QeBEZHJ9uW6hebsrYgbz8hJwUQao3TWCMtmfV8Nu1twOLAw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.31.tgz}
    name: '@jridgewell/trace-mapping'
    version: 0.3.31
    dependencies:
      '@jridgewell/resolve-uri': registry.npmjs.org/@jridgewell/resolve-uri/3.1.2
      '@jridgewell/sourcemap-codec': registry.npmjs.org/@jridgewell/sourcemap-codec/1.5.5
    dev: true

  registry.npmjs.org/@microsoft/fetch-event-source/2.0.1:
    resolution: {integrity: sha512-W6CLUJ2eBMw3Rec70qrsEW0jOm/3twwJv21mrmj2yORiaVmVYGS4sSS5yUwvQc1ZlDLYGPnClVWmUUMagKNsfA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@microsoft/fetch-event-source/-/fetch-event-source-2.0.1.tgz}
    name: '@microsoft/fetch-event-source'
    version: 2.0.1
    dev: false

  registry.npmjs.org/@nodelib/fs.scandir/2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz}
    name: '@nodelib/fs.scandir'
    version: 2.1.5
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': registry.npmjs.org/@nodelib/fs.stat/2.0.5
      run-parallel: registry.npmjs.org/run-parallel/1.2.0
    dev: true

  registry.npmjs.org/@nodelib/fs.stat/2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz}
    name: '@nodelib/fs.stat'
    version: 2.0.5
    engines: {node: '>= 8'}
    dev: true

  registry.npmjs.org/@nodelib/fs.walk/1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz}
    name: '@nodelib/fs.walk'
    version: 1.2.8
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': registry.npmjs.org/@nodelib/fs.scandir/2.1.5
      fastq: registry.npmjs.org/fastq/1.19.1
    dev: true

  registry.npmjs.org/@parcel/watcher-android-arm64/2.5.1:
    resolution: {integrity: sha512-KF8+j9nNbUN8vzOFDpRMsaKBHZ/mcjEjMToVMJOhTozkDonQFFrRcfdLWn6yWKCmJKmdVxSgHiYvTCef4/qcBA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher-android-arm64/-/watcher-android-arm64-2.5.1.tgz}
    name: '@parcel/watcher-android-arm64'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@parcel/watcher-darwin-arm64/2.5.1:
    resolution: {integrity: sha512-eAzPv5osDmZyBhou8PoF4i6RQXAfeKL9tjb3QzYuccXFMQU0ruIc/POh30ePnaOyD1UXdlKguHBmsTs53tVoPw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher-darwin-arm64/-/watcher-darwin-arm64-2.5.1.tgz}
    name: '@parcel/watcher-darwin-arm64'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@parcel/watcher-darwin-x64/2.5.1:
    resolution: {integrity: sha512-1ZXDthrnNmwv10A0/3AJNZ9JGlzrF82i3gNQcWOzd7nJ8aj+ILyW1MTxVk35Db0u91oD5Nlk9MBiujMlwmeXZg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher-darwin-x64/-/watcher-darwin-x64-2.5.1.tgz}
    name: '@parcel/watcher-darwin-x64'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@parcel/watcher-freebsd-x64/2.5.1:
    resolution: {integrity: sha512-SI4eljM7Flp9yPuKi8W0ird8TI/JK6CSxju3NojVI6BjHsTyK7zxA9urjVjEKJ5MBYC+bLmMcbAWlZ+rFkLpJQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.5.1.tgz}
    name: '@parcel/watcher-freebsd-x64'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@parcel/watcher-linux-arm-glibc/2.5.1:
    resolution: {integrity: sha512-RCdZlEyTs8geyBkkcnPWvtXLY44BCeZKmGYRtSgtwwnHR4dxfHRG3gR99XdMEdQ7KeiDdasJwwvNSF5jKtDwdA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.5.1.tgz}
    name: '@parcel/watcher-linux-arm-glibc'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@parcel/watcher-linux-arm-musl/2.5.1:
    resolution: {integrity: sha512-6E+m/Mm1t1yhB8X412stiKFG3XykmgdIOqhjWj+VL8oHkKABfu/gjFj8DvLrYVHSBNC+/u5PeNrujiSQ1zwd1Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher-linux-arm-musl/-/watcher-linux-arm-musl-2.5.1.tgz}
    name: '@parcel/watcher-linux-arm-musl'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@parcel/watcher-linux-arm64-glibc/2.5.1:
    resolution: {integrity: sha512-LrGp+f02yU3BN9A+DGuY3v3bmnFUggAITBGriZHUREfNEzZh/GO06FF5u2kx8x+GBEUYfyTGamol4j3m9ANe8w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.5.1.tgz}
    name: '@parcel/watcher-linux-arm64-glibc'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@parcel/watcher-linux-arm64-musl/2.5.1:
    resolution: {integrity: sha512-cFOjABi92pMYRXS7AcQv9/M1YuKRw8SZniCDw0ssQb/noPkRzA+HBDkwmyOJYp5wXcsTrhxO0zq1U11cK9jsFg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.5.1.tgz}
    name: '@parcel/watcher-linux-arm64-musl'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@parcel/watcher-linux-x64-glibc/2.5.1:
    resolution: {integrity: sha512-GcESn8NZySmfwlTsIur+49yDqSny2IhPeZfXunQi48DMugKeZ7uy1FX83pO0X22sHntJ4Ub+9k34XQCX+oHt2A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.5.1.tgz}
    name: '@parcel/watcher-linux-x64-glibc'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@parcel/watcher-linux-x64-musl/2.5.1:
    resolution: {integrity: sha512-n0E2EQbatQ3bXhcH2D1XIAANAcTZkQICBPVaxMeaCVBtOpBZpWJuf7LwyWPSBDITb7In8mqQgJ7gH8CILCURXg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.5.1.tgz}
    name: '@parcel/watcher-linux-x64-musl'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@parcel/watcher-win32-arm64/2.5.1:
    resolution: {integrity: sha512-RFzklRvmc3PkjKjry3hLF9wD7ppR4AKcWNzH7kXR7GUe0Igb3Nz8fyPwtZCSquGrhU5HhUNDr/mKBqj7tqA2Vw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.5.1.tgz}
    name: '@parcel/watcher-win32-arm64'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@parcel/watcher-win32-ia32/2.5.1:
    resolution: {integrity: sha512-c2KkcVN+NJmuA7CGlaGD1qJh1cLfDnQsHjE89E60vUEMlqduHGCdCLJCID5geFVM0dOtA3ZiIO8BoEQmzQVfpQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.5.1.tgz}
    name: '@parcel/watcher-win32-ia32'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@parcel/watcher-win32-x64/2.5.1:
    resolution: {integrity: sha512-9lHBdJITeNR++EvSQVUcaZoWupyHfXe1jZvGZ06O/5MflPcuPLtEphScIBL+AiCWBO46tDSHzWyD0uDmmZqsgA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.5.1.tgz}
    name: '@parcel/watcher-win32-x64'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@parcel/watcher/2.5.1:
    resolution: {integrity: sha512-dfUnCxiN9H4ap84DvD2ubjw+3vUNpstxa0TneY/Paat8a3R4uQZDLSvWjmznAY/DoahqTHl9V46HF/Zs3F29pg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@parcel/watcher/-/watcher-2.5.1.tgz}
    name: '@parcel/watcher'
    version: 2.5.1
    engines: {node: '>= 10.0.0'}
    requiresBuild: true
    dependencies:
      detect-libc: registry.npmjs.org/detect-libc/1.0.3
      is-glob: registry.npmjs.org/is-glob/4.0.3
      micromatch: registry.npmjs.org/micromatch/4.0.8
      node-addon-api: registry.npmjs.org/node-addon-api/7.1.1
    optionalDependencies:
      '@parcel/watcher-android-arm64': registry.npmjs.org/@parcel/watcher-android-arm64/2.5.1
      '@parcel/watcher-darwin-arm64': registry.npmjs.org/@parcel/watcher-darwin-arm64/2.5.1
      '@parcel/watcher-darwin-x64': registry.npmjs.org/@parcel/watcher-darwin-x64/2.5.1
      '@parcel/watcher-freebsd-x64': registry.npmjs.org/@parcel/watcher-freebsd-x64/2.5.1
      '@parcel/watcher-linux-arm-glibc': registry.npmjs.org/@parcel/watcher-linux-arm-glibc/2.5.1
      '@parcel/watcher-linux-arm-musl': registry.npmjs.org/@parcel/watcher-linux-arm-musl/2.5.1
      '@parcel/watcher-linux-arm64-glibc': registry.npmjs.org/@parcel/watcher-linux-arm64-glibc/2.5.1
      '@parcel/watcher-linux-arm64-musl': registry.npmjs.org/@parcel/watcher-linux-arm64-musl/2.5.1
      '@parcel/watcher-linux-x64-glibc': registry.npmjs.org/@parcel/watcher-linux-x64-glibc/2.5.1
      '@parcel/watcher-linux-x64-musl': registry.npmjs.org/@parcel/watcher-linux-x64-musl/2.5.1
      '@parcel/watcher-win32-arm64': registry.npmjs.org/@parcel/watcher-win32-arm64/2.5.1
      '@parcel/watcher-win32-ia32': registry.npmjs.org/@parcel/watcher-win32-ia32/2.5.1
      '@parcel/watcher-win32-x64': registry.npmjs.org/@parcel/watcher-win32-x64/2.5.1
    dev: true
    optional: true

  registry.npmjs.org/@pkgr/core/0.2.9:
    resolution: {integrity: sha512-QNqXyfVS2wm9hweSYD2O7F0G06uurj9kZ96TRQE5Y9hU7+tgdZwIkbAKc5Ocy1HxEY2kuDQa6cQ1WRs/O5LFKA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@pkgr/core/-/core-0.2.9.tgz}
    name: '@pkgr/core'
    version: 0.2.9
    engines: {node: ^12.20.0 || ^14.18.0 || >=16.0.0}
    dev: true

  registry.npmjs.org/@polka/url/1.0.0-next.29:
    resolution: {integrity: sha512-wwQAWhWSuHaag8c4q/KN/vCoeOJYshAIvMQwD4GpSb3OiZklFfvAgmj0VCBBImRpuF/aFgIRzllXlVX93Jevww==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.29.tgz}
    name: '@polka/url'
    version: 1.0.0-next.29
    dev: true

  registry.npmjs.org/@quansync/fs/0.1.5:
    resolution: {integrity: sha512-lNS9hL2aS2NZgNW7BBj+6EBl4rOf8l+tQ0eRY6JWCI8jI2kc53gSoqbjojU0OnAWhzoXiOjFyGsHcDGePB3lhA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@quansync/fs/-/fs-0.1.5.tgz}
    name: '@quansync/fs'
    version: 0.1.5
    dependencies:
      quansync: registry.npmjs.org/quansync/0.2.11
    dev: true

  registry.npmjs.org/@rollup/rollup-android-arm-eabi/4.52.2:
    resolution: {integrity: sha512-o3pcKzJgSGt4d74lSZ+OCnHwkKBeAbFDmbEm5gg70eA8VkyCuC/zV9TwBnmw6VjDlRdF4Pshfb+WE9E6XY1PoQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.52.2.tgz}
    name: '@rollup/rollup-android-arm-eabi'
    version: 4.52.2
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-android-arm64/4.52.2:
    resolution: {integrity: sha512-cqFSWO5tX2vhC9hJTK8WAiPIm4Q8q/cU8j2HQA0L3E1uXvBYbOZMhE2oFL8n2pKB5sOCHY6bBuHaRwG7TkfJyw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.52.2.tgz}
    name: '@rollup/rollup-android-arm64'
    version: 4.52.2
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-darwin-arm64/4.52.2:
    resolution: {integrity: sha512-vngduywkkv8Fkh3wIZf5nFPXzWsNsVu1kvtLETWxTFf/5opZmflgVSeLgdHR56RQh71xhPhWoOkEBvbehwTlVA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.52.2.tgz}
    name: '@rollup/rollup-darwin-arm64'
    version: 4.52.2
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-darwin-x64/4.52.2:
    resolution: {integrity: sha512-h11KikYrUCYTrDj6h939hhMNlqU2fo/X4NB0OZcys3fya49o1hmFaczAiJWVAFgrM1NCP6RrO7lQKeVYSKBPSQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.52.2.tgz}
    name: '@rollup/rollup-darwin-x64'
    version: 4.52.2
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-freebsd-arm64/4.52.2:
    resolution: {integrity: sha512-/eg4CI61ZUkLXxMHyVlmlGrSQZ34xqWlZNW43IAU4RmdzWEx0mQJ2mN/Cx4IHLVZFL6UBGAh+/GXhgvGb+nVxw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.52.2.tgz}
    name: '@rollup/rollup-freebsd-arm64'
    version: 4.52.2
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-freebsd-x64/4.52.2:
    resolution: {integrity: sha512-QOWgFH5X9+p+S1NAfOqc0z8qEpJIoUHf7OWjNUGOeW18Mx22lAUOiA9b6r2/vpzLdfxi/f+VWsYjUOMCcYh0Ng==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.52.2.tgz}
    name: '@rollup/rollup-freebsd-x64'
    version: 4.52.2
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/4.52.2:
    resolution: {integrity: sha512-kDWSPafToDd8LcBYd1t5jw7bD5Ojcu12S3uT372e5HKPzQt532vW+rGFFOaiR0opxePyUkHrwz8iWYEyH1IIQA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.52.2.tgz}
    name: '@rollup/rollup-linux-arm-gnueabihf'
    version: 4.52.2
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/4.52.2:
    resolution: {integrity: sha512-gKm7Mk9wCv6/rkzwCiUC4KnevYhlf8ztBrDRT9g/u//1fZLapSRc+eDZj2Eu2wpJ+0RzUKgtNijnVIB4ZxyL+w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.52.2.tgz}
    name: '@rollup/rollup-linux-arm-musleabihf'
    version: 4.52.2
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/4.52.2:
    resolution: {integrity: sha512-66lA8vnj5mB/rtDNwPgrrKUOtCLVQypkyDa2gMfOefXK6rcZAxKLO9Fy3GkW8VkPnENv9hBkNOFfGLf6rNKGUg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.52.2.tgz}
    name: '@rollup/rollup-linux-arm64-gnu'
    version: 4.52.2
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-linux-arm64-musl/4.52.2:
    resolution: {integrity: sha512-s+OPucLNdJHvuZHuIz2WwncJ+SfWHFEmlC5nKMUgAelUeBUnlB4wt7rXWiyG4Zn07uY2Dd+SGyVa9oyLkVGOjA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.52.2.tgz}
    name: '@rollup/rollup-linux-arm64-musl'
    version: 4.52.2
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-linux-loong64-gnu/4.52.2:
    resolution: {integrity: sha512-8wTRM3+gVMDLLDdaT6tKmOE3lJyRy9NpJUS/ZRWmLCmOPIJhVyXwjBo+XbrrwtV33Em1/eCTd5TuGJm4+DmYjw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-linux-loong64-gnu/-/rollup-linux-loong64-gnu-4.52.2.tgz}
    name: '@rollup/rollup-linux-loong64-gnu'
    version: 4.52.2
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-linux-ppc64-gnu/4.52.2:
    resolution: {integrity: sha512-6yqEfgJ1anIeuP2P/zhtfBlDpXUb80t8DpbYwXQ3bQd95JMvUaqiX+fKqYqUwZXqdJDd8xdilNtsHM2N0cFm6A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-linux-ppc64-gnu/-/rollup-linux-ppc64-gnu-4.52.2.tgz}
    name: '@rollup/rollup-linux-ppc64-gnu'
    version: 4.52.2
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/4.52.2:
    resolution: {integrity: sha512-sshYUiYVSEI2B6dp4jMncwxbrUqRdNApF2c3bhtLAU0qA8Lrri0p0NauOsTWh3yCCCDyBOjESHMExonp7Nzc0w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.52.2.tgz}
    name: '@rollup/rollup-linux-riscv64-gnu'
    version: 4.52.2
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/4.52.2:
    resolution: {integrity: sha512-duBLgd+3pqC4MMwBrKkFxaZerUxZcYApQVC5SdbF5/e/589GwVvlRUnyqMFbM8iUSb1BaoX/3fRL7hB9m2Pj8Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.52.2.tgz}
    name: '@rollup/rollup-linux-riscv64-musl'
    version: 4.52.2
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/4.52.2:
    resolution: {integrity: sha512-tzhYJJidDUVGMgVyE+PmxENPHlvvqm1KILjjZhB8/xHYqAGeizh3GBGf9u6WdJpZrz1aCpIIHG0LgJgH9rVjHQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.52.2.tgz}
    name: '@rollup/rollup-linux-s390x-gnu'
    version: 4.52.2
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-linux-x64-gnu/4.52.2:
    resolution: {integrity: sha512-opH8GSUuVcCSSyHHcl5hELrmnk4waZoVpgn/4FDao9iyE4WpQhyWJ5ryl5M3ocp4qkRuHfyXnGqg8M9oKCEKRA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.52.2.tgz}
    name: '@rollup/rollup-linux-x64-gnu'
    version: 4.52.2
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-linux-x64-musl/4.52.2:
    resolution: {integrity: sha512-LSeBHnGli1pPKVJ79ZVJgeZWWZXkEe/5o8kcn23M8eMKCUANejchJbF/JqzM4RRjOJfNRhKJk8FuqL1GKjF5oQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.52.2.tgz}
    name: '@rollup/rollup-linux-x64-musl'
    version: 4.52.2
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-openharmony-arm64/4.52.2:
    resolution: {integrity: sha512-uPj7MQ6/s+/GOpolavm6BPo+6CbhbKYyZHUDvZ/SmJM7pfDBgdGisFX3bY/CBDMg2ZO4utfhlApkSfZ92yXw7Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-openharmony-arm64/-/rollup-openharmony-arm64-4.52.2.tgz}
    name: '@rollup/rollup-openharmony-arm64'
    version: 4.52.2
    cpu: [arm64]
    os: [openharmony]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/4.52.2:
    resolution: {integrity: sha512-Z9MUCrSgIaUeeHAiNkm3cQyst2UhzjPraR3gYYfOjAuZI7tcFRTOD+4cHLPoS/3qinchth+V56vtqz1Tv+6KPA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.52.2.tgz}
    name: '@rollup/rollup-win32-arm64-msvc'
    version: 4.52.2
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/4.52.2:
    resolution: {integrity: sha512-+GnYBmpjldD3XQd+HMejo+0gJGwYIOfFeoBQv32xF/RUIvccUz20/V6Otdv+57NE70D5pa8W/jVGDoGq0oON4A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.52.2.tgz}
    name: '@rollup/rollup-win32-ia32-msvc'
    version: 4.52.2
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-win32-x64-gnu/4.52.2:
    resolution: {integrity: sha512-ApXFKluSB6kDQkAqZOKXBjiaqdF1BlKi+/eqnYe9Ee7U2K3pUDKsIyr8EYm/QDHTJIM+4X+lI0gJc3TTRhd+dA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-win32-x64-gnu/-/rollup-win32-x64-gnu-4.52.2.tgz}
    name: '@rollup/rollup-win32-x64-gnu'
    version: 4.52.2
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@rollup/rollup-win32-x64-msvc/4.52.2:
    resolution: {integrity: sha512-ARz+Bs8kY6FtitYM96PqPEVvPXqEZmPZsSkXvyX19YzDqkCaIlhCieLLMI5hxO9SRZ2XtCtm8wxhy0iJ2jxNfw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.52.2.tgz}
    name: '@rollup/rollup-win32-x64-msvc'
    version: 4.52.2
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/@sec-ant/readable-stream/0.4.1:
    resolution: {integrity: sha512-831qok9r2t8AlxLko40y2ebgSDhenenCatLVeW/uBtnHPyhHOvG0C7TvfgecV+wHzIm5KUICgzmVpWS+IMEAeg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@sec-ant/readable-stream/-/readable-stream-0.4.1.tgz}
    name: '@sec-ant/readable-stream'
    version: 0.4.1
    dev: true

  registry.npmjs.org/@sindresorhus/merge-streams/4.0.0:
    resolution: {integrity: sha512-tlqY9xq5ukxTUZBmoOp+m61cqwQD5pHJtFY3Mn8CA8ps6yghLH/Hw8UPdqg4OLmFW3IFlcXnQNmo/dh8HzXYIQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@sindresorhus/merge-streams/-/merge-streams-4.0.0.tgz}
    name: '@sindresorhus/merge-streams'
    version: 4.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmjs.org/@tailwindcss/typography/0.5.19:
    resolution: {integrity: sha512-w31dd8HOx3k9vPtcQh5QHP9GwKcgbMp87j58qi6xgiBnFFtKEAgCWnDw4qUT8aHwkCp8bKvb/KGKWWHedP0AAg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@tailwindcss/typography/-/typography-0.5.19.tgz}
    name: '@tailwindcss/typography'
    version: 0.5.19
    peerDependencies:
      tailwindcss: '>=3.0.0 || insiders || >=4.0.0-alpha.20 || >=4.0.0-beta.1'
    dependencies:
      postcss-selector-parser: registry.npmjs.org/postcss-selector-parser/6.0.10
    dev: true

  registry.npmjs.org/@tsconfig/node22/22.0.2:
    resolution: {integrity: sha512-Kmwj4u8sDRDrMYRoN9FDEcXD8UpBSaPQQ24Gz+Gamqfm7xxn+GBR7ge/Z7pK8OXNGyUzbSwJj+TH6B+DS/epyA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@tsconfig/node22/-/node22-22.0.2.tgz}
    name: '@tsconfig/node22'
    version: 22.0.2
    dev: true

  registry.npmjs.org/@types/estree/1.0.8:
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz}
    name: '@types/estree'
    version: 1.0.8
    dev: true

  registry.npmjs.org/@types/json-schema/7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz}
    name: '@types/json-schema'
    version: 7.0.15
    dev: true

  registry.npmjs.org/@types/node/22.18.6:
    resolution: {integrity: sha512-r8uszLPpeIWbNKtvWRt/DbVi5zbqZyj1PTmhRMqBMvDnaz1QpmSKujUtJLrqGZeoM8v72MfYggDceY4K1itzWQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@types/node/-/node-22.18.6.tgz}
    name: '@types/node'
    version: 22.18.6
    dependencies:
      undici-types: registry.npmjs.org/undici-types/6.21.0
    dev: true

  registry.npmjs.org/@typescript-eslint/eslint-plugin/8.44.1_zl5kzazaahwldyhrwpyoeq2o44:
    resolution: {integrity: sha512-molgphGqOBT7t4YKCSkbasmu1tb1MgrZ2szGzHbclF7PNmOkSTQVHy+2jXOSnxvR3+Xe1yySHFZoqMpz3TfQsw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.44.1.tgz}
    id: registry.npmjs.org/@typescript-eslint/eslint-plugin/8.44.1
    name: '@typescript-eslint/eslint-plugin'
    version: 8.44.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.44.1
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'
    dependencies:
      '@eslint-community/regexpp': registry.npmjs.org/@eslint-community/regexpp/4.12.1
      '@typescript-eslint/parser': registry.npmjs.org/@typescript-eslint/parser/8.44.1_mgbfndoj365cbyr3vqehozbar4
      '@typescript-eslint/scope-manager': registry.npmjs.org/@typescript-eslint/scope-manager/8.44.1
      '@typescript-eslint/type-utils': registry.npmjs.org/@typescript-eslint/type-utils/8.44.1_mgbfndoj365cbyr3vqehozbar4
      '@typescript-eslint/utils': registry.npmjs.org/@typescript-eslint/utils/8.44.1_mgbfndoj365cbyr3vqehozbar4
      '@typescript-eslint/visitor-keys': registry.npmjs.org/@typescript-eslint/visitor-keys/8.44.1
      eslint: registry.npmjs.org/eslint/9.36.0_jiti@2.6.0
      graphemer: registry.npmjs.org/graphemer/1.4.0
      ignore: registry.npmjs.org/ignore/7.0.5
      natural-compare: registry.npmjs.org/natural-compare/1.4.0
      ts-api-utils: registry.npmjs.org/ts-api-utils/2.1.0_typescript@5.8.3
      typescript: registry.npmjs.org/typescript/5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@typescript-eslint/parser/8.44.1_mgbfndoj365cbyr3vqehozbar4:
    resolution: {integrity: sha512-EHrrEsyhOhxYt8MTg4zTF+DJMuNBzWwgvvOYNj/zm1vnaD/IC5zCXFehZv94Piqa2cRFfXrTFxIvO95L7Qc/cw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@typescript-eslint/parser/-/parser-8.44.1.tgz}
    id: registry.npmjs.org/@typescript-eslint/parser/8.44.1
    name: '@typescript-eslint/parser'
    version: 8.44.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'
    dependencies:
      '@typescript-eslint/scope-manager': registry.npmjs.org/@typescript-eslint/scope-manager/8.44.1
      '@typescript-eslint/types': registry.npmjs.org/@typescript-eslint/types/8.44.1
      '@typescript-eslint/typescript-estree': registry.npmjs.org/@typescript-eslint/typescript-estree/8.44.1_typescript@5.8.3
      '@typescript-eslint/visitor-keys': registry.npmjs.org/@typescript-eslint/visitor-keys/8.44.1
      debug: registry.npmjs.org/debug/4.4.3
      eslint: registry.npmjs.org/eslint/9.36.0_jiti@2.6.0
      typescript: registry.npmjs.org/typescript/5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@typescript-eslint/project-service/8.44.1_typescript@5.8.3:
    resolution: {integrity: sha512-ycSa60eGg8GWAkVsKV4E6Nz33h+HjTXbsDT4FILyL8Obk5/mx4tbvCNsLf9zret3ipSumAOG89UcCs/KRaKYrA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.44.1.tgz}
    id: registry.npmjs.org/@typescript-eslint/project-service/8.44.1
    name: '@typescript-eslint/project-service'
    version: 8.44.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <6.0.0'
    dependencies:
      '@typescript-eslint/tsconfig-utils': registry.npmjs.org/@typescript-eslint/tsconfig-utils/8.44.1_typescript@5.8.3
      '@typescript-eslint/types': registry.npmjs.org/@typescript-eslint/types/8.44.1
      debug: registry.npmjs.org/debug/4.4.3
      typescript: registry.npmjs.org/typescript/5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@typescript-eslint/scope-manager/8.44.1:
    resolution: {integrity: sha512-NdhWHgmynpSvyhchGLXh+w12OMT308Gm25JoRIyTZqEbApiBiQHD/8xgb6LqCWCFcxFtWwaVdFsLPQI3jvhywg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-8.44.1.tgz}
    name: '@typescript-eslint/scope-manager'
    version: 8.44.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@typescript-eslint/types': registry.npmjs.org/@typescript-eslint/types/8.44.1
      '@typescript-eslint/visitor-keys': registry.npmjs.org/@typescript-eslint/visitor-keys/8.44.1
    dev: true

  registry.npmjs.org/@typescript-eslint/tsconfig-utils/8.44.1_typescript@5.8.3:
    resolution: {integrity: sha512-B5OyACouEjuIvof3o86lRMvyDsFwZm+4fBOqFHccIctYgBjqR3qT39FBYGN87khcgf0ExpdCBeGKpKRhSFTjKQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.44.1.tgz}
    id: registry.npmjs.org/@typescript-eslint/tsconfig-utils/8.44.1
    name: '@typescript-eslint/tsconfig-utils'
    version: 8.44.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <6.0.0'
    dependencies:
      typescript: registry.npmjs.org/typescript/5.8.3
    dev: true

  registry.npmjs.org/@typescript-eslint/type-utils/8.44.1_mgbfndoj365cbyr3vqehozbar4:
    resolution: {integrity: sha512-KdEerZqHWXsRNKjF9NYswNISnFzXfXNDfPxoTh7tqohU/PRIbwTmsjGK6V9/RTYWau7NZvfo52lgVk+sJh0K3g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-8.44.1.tgz}
    id: registry.npmjs.org/@typescript-eslint/type-utils/8.44.1
    name: '@typescript-eslint/type-utils'
    version: 8.44.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'
    dependencies:
      '@typescript-eslint/types': registry.npmjs.org/@typescript-eslint/types/8.44.1
      '@typescript-eslint/typescript-estree': registry.npmjs.org/@typescript-eslint/typescript-estree/8.44.1_typescript@5.8.3
      '@typescript-eslint/utils': registry.npmjs.org/@typescript-eslint/utils/8.44.1_mgbfndoj365cbyr3vqehozbar4
      debug: registry.npmjs.org/debug/4.4.3
      eslint: registry.npmjs.org/eslint/9.36.0_jiti@2.6.0
      ts-api-utils: registry.npmjs.org/ts-api-utils/2.1.0_typescript@5.8.3
      typescript: registry.npmjs.org/typescript/5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@typescript-eslint/types/8.44.1:
    resolution: {integrity: sha512-Lk7uj7y9uQUOEguiDIDLYLJOrYHQa7oBiURYVFqIpGxclAFQ78f6VUOM8lI2XEuNOKNB7XuvM2+2cMXAoq4ALQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@typescript-eslint/types/-/types-8.44.1.tgz}
    name: '@typescript-eslint/types'
    version: 8.44.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmjs.org/@typescript-eslint/typescript-estree/8.44.1_typescript@5.8.3:
    resolution: {integrity: sha512-qnQJ+mVa7szevdEyvfItbO5Vo+GfZ4/GZWWDRRLjrxYPkhM+6zYB2vRYwCsoJLzqFCdZT4mEqyJoyzkunsZ96A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-8.44.1.tgz}
    id: registry.npmjs.org/@typescript-eslint/typescript-estree/8.44.1
    name: '@typescript-eslint/typescript-estree'
    version: 8.44.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <6.0.0'
    dependencies:
      '@typescript-eslint/project-service': registry.npmjs.org/@typescript-eslint/project-service/8.44.1_typescript@5.8.3
      '@typescript-eslint/tsconfig-utils': registry.npmjs.org/@typescript-eslint/tsconfig-utils/8.44.1_typescript@5.8.3
      '@typescript-eslint/types': registry.npmjs.org/@typescript-eslint/types/8.44.1
      '@typescript-eslint/visitor-keys': registry.npmjs.org/@typescript-eslint/visitor-keys/8.44.1
      debug: registry.npmjs.org/debug/4.4.3
      fast-glob: registry.npmjs.org/fast-glob/3.3.3
      is-glob: registry.npmjs.org/is-glob/4.0.3
      minimatch: registry.npmjs.org/minimatch/9.0.5
      semver: registry.npmjs.org/semver/7.7.2
      ts-api-utils: registry.npmjs.org/ts-api-utils/2.1.0_typescript@5.8.3
      typescript: registry.npmjs.org/typescript/5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@typescript-eslint/utils/8.44.1_mgbfndoj365cbyr3vqehozbar4:
    resolution: {integrity: sha512-DpX5Fp6edTlocMCwA+mHY8Mra+pPjRZ0TfHkXI8QFelIKcbADQz1LUPNtzOFUriBB2UYqw4Pi9+xV4w9ZczHFg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@typescript-eslint/utils/-/utils-8.44.1.tgz}
    id: registry.npmjs.org/@typescript-eslint/utils/8.44.1
    name: '@typescript-eslint/utils'
    version: 8.44.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'
    dependencies:
      '@eslint-community/eslint-utils': registry.npmjs.org/@eslint-community/eslint-utils/4.9.0_eslint@9.36.0
      '@typescript-eslint/scope-manager': registry.npmjs.org/@typescript-eslint/scope-manager/8.44.1
      '@typescript-eslint/types': registry.npmjs.org/@typescript-eslint/types/8.44.1
      '@typescript-eslint/typescript-estree': registry.npmjs.org/@typescript-eslint/typescript-estree/8.44.1_typescript@5.8.3
      eslint: registry.npmjs.org/eslint/9.36.0_jiti@2.6.0
      typescript: registry.npmjs.org/typescript/5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@typescript-eslint/visitor-keys/8.44.1:
    resolution: {integrity: sha512-576+u0QD+Jp3tZzvfRfxon0EA2lzcDt3lhUbsC6Lgzy9x2VR4E+JUiNyGHi5T8vk0TV+fpJ5GLG1JsJuWCaKhw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-8.44.1.tgz}
    name: '@typescript-eslint/visitor-keys'
    version: 8.44.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      '@typescript-eslint/types': registry.npmjs.org/@typescript-eslint/types/8.44.1
      eslint-visitor-keys: registry.npmjs.org/eslint-visitor-keys/4.2.1
    dev: true

  registry.npmjs.org/@unocss/astro/66.5.2_vite@6.3.6:
    resolution: {integrity: sha512-JUiJL4wkDTCFgReQ+c1Nqb47EfryJvGiSp9MxXclCUbp5hegqq7yMg3BMpJ4QzHmf5EeDFO38eRBKV57hd0Iew==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/astro/-/astro-66.5.2.tgz}
    id: registry.npmjs.org/@unocss/astro/66.5.2
    name: '@unocss/astro'
    version: 66.5.2
    peerDependencies:
      vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0 || ^7.0.0-0
    peerDependenciesMeta:
      vite:
        optional: true
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      '@unocss/reset': registry.npmjs.org/@unocss/reset/66.5.2
      '@unocss/vite': registry.npmjs.org/@unocss/vite/66.5.2_vite@6.3.6
      vite: registry.npmjs.org/vite/6.3.6_nnwfsygotcqvs65kz7hc73f7s4
    dev: true

  registry.npmjs.org/@unocss/cli/66.5.2:
    resolution: {integrity: sha512-WFj3fd5LqPX2/NvG/kX4vxML14F5yU6e0yPezO+7fjrJ9V31m1AFQWfiT2p8HbNUcQd9jZ9lcoWLm3Q1FsdPDA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/cli/-/cli-66.5.2.tgz}
    name: '@unocss/cli'
    version: 66.5.2
    engines: {node: '>=14'}
    hasBin: true
    dependencies:
      '@jridgewell/remapping': registry.npmjs.org/@jridgewell/remapping/2.3.5
      '@unocss/config': registry.npmjs.org/@unocss/config/66.5.2
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      '@unocss/preset-uno': registry.npmjs.org/@unocss/preset-uno/66.5.2
      cac: registry.npmjs.org/cac/6.7.14
      chokidar: registry.npmjs.org/chokidar/3.6.0
      colorette: registry.npmjs.org/colorette/2.0.20
      consola: registry.npmjs.org/consola/3.4.2
      magic-string: registry.npmjs.org/magic-string/0.30.19
      pathe: registry.npmjs.org/pathe/2.0.3
      perfect-debounce: registry.npmjs.org/perfect-debounce/1.0.0
      tinyglobby: registry.npmjs.org/tinyglobby/0.2.15
      unplugin-utils: registry.npmjs.org/unplugin-utils/0.3.0
    dev: true

  registry.npmjs.org/@unocss/config/66.5.2:
    resolution: {integrity: sha512-rREBBt2a6aZJ21TCeKG3/wjHfTNPbIwdrJtIVrN7hLcljW2vnWuyYabZ1yASK8+lnNsMoBoU5mbakgrPF0MItA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/config/-/config-66.5.2.tgz}
    name: '@unocss/config'
    version: 66.5.2
    engines: {node: '>=14'}
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      unconfig: registry.npmjs.org/unconfig/7.3.3
    dev: true

  registry.npmjs.org/@unocss/core/66.5.2:
    resolution: {integrity: sha512-POSEpwj2FJtrDgzSq6nVhAJbnGIYPqtEMTpzQXfeFqPDMidAXjaH/xZUeTdHDbI9Jg700smrRXJtFJrJFXkmiQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/core/-/core-66.5.2.tgz}
    name: '@unocss/core'
    version: 66.5.2
    dev: true

  registry.npmjs.org/@unocss/extractor-arbitrary-variants/66.5.2:
    resolution: {integrity: sha512-MNHzhA4RKJJVo6D5Uc+SkPfeugO1KXDt0GFg0FkOUKTTnahxyXNvd9BG9HHYlKSiaYCgUhFmysNhv04Gza+CNg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/extractor-arbitrary-variants/-/extractor-arbitrary-variants-66.5.2.tgz}
    name: '@unocss/extractor-arbitrary-variants'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
    dev: true

  registry.npmjs.org/@unocss/inspector/66.5.2:
    resolution: {integrity: sha512-8PuM01lrsOuyas3K+5LqeoeiujIGk72ivvJsP4/T8h03XQWzpS7NPJU6JVJQUcYZAE+WtqFcPJ8wcg0ERKNPdA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/inspector/-/inspector-66.5.2.tgz}
    name: '@unocss/inspector'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      '@unocss/rule-utils': registry.npmjs.org/@unocss/rule-utils/66.5.2
      colorette: registry.npmjs.org/colorette/2.0.20
      gzip-size: registry.npmjs.org/gzip-size/6.0.0
      sirv: registry.npmjs.org/sirv/3.0.2
      vue-flow-layout: registry.npmjs.org/vue-flow-layout/0.2.0
    dev: true

  registry.npmjs.org/@unocss/postcss/66.5.2_postcss@8.5.6:
    resolution: {integrity: sha512-tZrWVcGm1cJghYqRFgiCb/HCnWehdJ3/6lUlXN5Ogfu4agCa3f8QES43+6TMpuTKdqkjXvMI3jZFKNMgN+/wlg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/postcss/-/postcss-66.5.2.tgz}
    id: registry.npmjs.org/@unocss/postcss/66.5.2
    name: '@unocss/postcss'
    version: 66.5.2
    engines: {node: '>=14'}
    peerDependencies:
      postcss: ^8.4.21
    dependencies:
      '@unocss/config': registry.npmjs.org/@unocss/config/66.5.2
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      '@unocss/rule-utils': registry.npmjs.org/@unocss/rule-utils/66.5.2
      css-tree: registry.npmjs.org/css-tree/3.1.0
      postcss: registry.npmjs.org/postcss/8.5.6
      tinyglobby: registry.npmjs.org/tinyglobby/0.2.15
    dev: true

  registry.npmjs.org/@unocss/preset-attributify/66.5.2:
    resolution: {integrity: sha512-/FigYbT1uA5DLy5dVV2QuTizvSge8jZZZu3uGAu25p59m/h/6ZjvkCoiKcTkvmNUuZfj/ZPZmAE8GoSn1uR++A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/preset-attributify/-/preset-attributify-66.5.2.tgz}
    name: '@unocss/preset-attributify'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
    dev: true

  registry.npmjs.org/@unocss/preset-icons/66.5.2:
    resolution: {integrity: sha512-vjSwttkZrU8FfIo4TCkSOAIba0xbWE6N3/xEdK3tjq+FSgClzs9SmO06KLJHSntJ/N5JYA0wpkPS5mLYxGMwqw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/preset-icons/-/preset-icons-66.5.2.tgz}
    name: '@unocss/preset-icons'
    version: 66.5.2
    dependencies:
      '@iconify/utils': registry.npmjs.org/@iconify/utils/3.0.2
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      ofetch: registry.npmjs.org/ofetch/1.4.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@unocss/preset-mini/66.5.2:
    resolution: {integrity: sha512-YLOuYq7GNoWNgF3P41AtcvnOodSP49x0RNM4PR/ntGddl0BfsFaKeCGzt8DpbvavhQpBn0+kt4GP3RajKooAIQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/preset-mini/-/preset-mini-66.5.2.tgz}
    name: '@unocss/preset-mini'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      '@unocss/extractor-arbitrary-variants': registry.npmjs.org/@unocss/extractor-arbitrary-variants/66.5.2
      '@unocss/rule-utils': registry.npmjs.org/@unocss/rule-utils/66.5.2
    dev: true

  registry.npmjs.org/@unocss/preset-tagify/66.5.2:
    resolution: {integrity: sha512-6AusDr1rD+HK22F4kwPLWqOImV3W+0nyPMsUwLVHQeaZktpSFSqaIQCI6aIVWyftvW/paST1Xc4HEHb7rKBF/w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/preset-tagify/-/preset-tagify-66.5.2.tgz}
    name: '@unocss/preset-tagify'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
    dev: true

  registry.npmjs.org/@unocss/preset-typography/66.5.2:
    resolution: {integrity: sha512-Ez3VWrNlJVa9cywsI/IwUdZ4OUeeUvf04pZmf+bwSU3CHqfonRT8K3+ndHQfuTJYbIb1k3few+cc1P1W7NP7Xw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/preset-typography/-/preset-typography-66.5.2.tgz}
    name: '@unocss/preset-typography'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      '@unocss/rule-utils': registry.npmjs.org/@unocss/rule-utils/66.5.2
    dev: true

  registry.npmjs.org/@unocss/preset-uno/66.5.2:
    resolution: {integrity: sha512-+raFp6uRwvQVIS7y8JoQI+5PPodl+uNsHxL9uH/JkelB5++ACrcP/ShN8RrDD97K+wtSP+3kr9SsK6dk0f2Mpg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/preset-uno/-/preset-uno-66.5.2.tgz}
    name: '@unocss/preset-uno'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      '@unocss/preset-wind3': registry.npmjs.org/@unocss/preset-wind3/66.5.2
    dev: true

  registry.npmjs.org/@unocss/preset-web-fonts/66.5.2:
    resolution: {integrity: sha512-xMFUE8Bhe2X/VlUBtdXTnDrrZL+WE99RaiBNLS1F1Na5r4Fc5Ck0p8a+SnMB7GDx5gtwf1ekKwi0pAP8+vIJnQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/preset-web-fonts/-/preset-web-fonts-66.5.2.tgz}
    name: '@unocss/preset-web-fonts'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      ofetch: registry.npmjs.org/ofetch/1.4.1
    dev: true

  registry.npmjs.org/@unocss/preset-wind/66.5.2:
    resolution: {integrity: sha512-jJN7kLXNAn/6VpYWTrIJGsXAhziPlPhK7bdnilbVnrlTSOluG6peCE6gdUyjdlLDyYELzz8qZ7ZvOo77IsBkPQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/preset-wind/-/preset-wind-66.5.2.tgz}
    name: '@unocss/preset-wind'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      '@unocss/preset-wind3': registry.npmjs.org/@unocss/preset-wind3/66.5.2
    dev: true

  registry.npmjs.org/@unocss/preset-wind3/66.5.2:
    resolution: {integrity: sha512-qgzLiPd6CkepLLssBod7ejQ4sKKqAvCOyjqpp0eFmHVUKGBEGPzOI1/WnbrAzvTHDonbSc52kB/XEWlgWmDhhA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/preset-wind3/-/preset-wind3-66.5.2.tgz}
    name: '@unocss/preset-wind3'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      '@unocss/preset-mini': registry.npmjs.org/@unocss/preset-mini/66.5.2
      '@unocss/rule-utils': registry.npmjs.org/@unocss/rule-utils/66.5.2
    dev: true

  registry.npmjs.org/@unocss/preset-wind4/66.5.2:
    resolution: {integrity: sha512-493Vb1do+05+3tdE0kU+SUKAPG9Spd+hItKfc09OL276T1DMj7AZzIq5q+rj9e+bOAjWAAutjw94RPNjKlU3fA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/preset-wind4/-/preset-wind4-66.5.2.tgz}
    name: '@unocss/preset-wind4'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      '@unocss/extractor-arbitrary-variants': registry.npmjs.org/@unocss/extractor-arbitrary-variants/66.5.2
      '@unocss/rule-utils': registry.npmjs.org/@unocss/rule-utils/66.5.2
    dev: true

  registry.npmjs.org/@unocss/reset/66.5.2:
    resolution: {integrity: sha512-DirXdqrkSp3fThRGOz0s0ehsYBpLb72Vh4QlPfMFuwHHFC9P9IDTLMUj5kD51A9fdy07Wrmhs7T5CQ//DlfOdQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/reset/-/reset-66.5.2.tgz}
    name: '@unocss/reset'
    version: 66.5.2
    dev: true

  registry.npmjs.org/@unocss/rule-utils/66.5.2:
    resolution: {integrity: sha512-2eR5TBTO+cmPY9ahFjyEu8qP/NFPI02dVpI0rgGKdyDMv/PnO9+yS/9rKgrmXsN3nPYHjOrLutRXkF/xxm/t3w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/rule-utils/-/rule-utils-66.5.2.tgz}
    name: '@unocss/rule-utils'
    version: 66.5.2
    engines: {node: '>=14'}
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      magic-string: registry.npmjs.org/magic-string/0.30.19
    dev: true

  registry.npmjs.org/@unocss/transformer-attributify-jsx/66.5.2:
    resolution: {integrity: sha512-mTa+fMKVz96He21E6FYCJyd0QbL6Xr5JjdqZEEFZiwt9N884g89pHZOlEURmrkQBrWc5NwSfzNB7lCkhuUOIFQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/transformer-attributify-jsx/-/transformer-attributify-jsx-66.5.2.tgz}
    name: '@unocss/transformer-attributify-jsx'
    version: 66.5.2
    dependencies:
      '@babel/parser': registry.npmjs.org/@babel/parser/7.27.7
      '@babel/traverse': registry.npmjs.org/@babel/traverse/7.27.7
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@unocss/transformer-compile-class/66.5.2:
    resolution: {integrity: sha512-50UTeKH6zycAzF44+6eCW13uPy5bw3W3Z8miEAIj0cNaKHTul0QYQFhLT3R804cnkAdX/Cp6IE/HSivxeP5ueQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/transformer-compile-class/-/transformer-compile-class-66.5.2.tgz}
    name: '@unocss/transformer-compile-class'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
    dev: true

  registry.npmjs.org/@unocss/transformer-directives/66.5.2:
    resolution: {integrity: sha512-hwbAdV1Vr001ojaXR8rVt4jJvPnrAjl9h2SQWjaqyGkLntnKvFB8JSTS9CT0cyv1GrwiBAwdnVIdioLAQ3GPbg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/transformer-directives/-/transformer-directives-66.5.2.tgz}
    name: '@unocss/transformer-directives'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      '@unocss/rule-utils': registry.npmjs.org/@unocss/rule-utils/66.5.2
      css-tree: registry.npmjs.org/css-tree/3.1.0
    dev: true

  registry.npmjs.org/@unocss/transformer-variant-group/66.5.2:
    resolution: {integrity: sha512-ZgH4hgoIbbh92pszsT2M93e/DcEmN+s9yjYPPCa0qxvTQb6aANM02Z6T7OE0ltQ0NShELfIS4oSGUKY6ezHwug==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/transformer-variant-group/-/transformer-variant-group-66.5.2.tgz}
    name: '@unocss/transformer-variant-group'
    version: 66.5.2
    dependencies:
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
    dev: true

  registry.npmjs.org/@unocss/vite/66.5.2_vite@6.3.6:
    resolution: {integrity: sha512-0OcvZHV7ag8ml9z1pG0T92b81CP8nOv21o9vZnQUJN4Uw+8fnVA9xCh1X68IfDNr5Q8nS5zz/Fjr/pC89Cb+og==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@unocss/vite/-/vite-66.5.2.tgz}
    id: registry.npmjs.org/@unocss/vite/66.5.2
    name: '@unocss/vite'
    version: 66.5.2
    peerDependencies:
      vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0 || ^7.0.0-0
    dependencies:
      '@jridgewell/remapping': registry.npmjs.org/@jridgewell/remapping/2.3.5
      '@unocss/config': registry.npmjs.org/@unocss/config/66.5.2
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      '@unocss/inspector': registry.npmjs.org/@unocss/inspector/66.5.2
      chokidar: registry.npmjs.org/chokidar/3.6.0
      magic-string: registry.npmjs.org/magic-string/0.30.19
      pathe: registry.npmjs.org/pathe/2.0.3
      tinyglobby: registry.npmjs.org/tinyglobby/0.2.15
      unplugin-utils: registry.npmjs.org/unplugin-utils/0.3.0
      vite: registry.npmjs.org/vite/6.3.6_nnwfsygotcqvs65kz7hc73f7s4
    dev: true

  registry.npmjs.org/@vitejs/plugin-vue/5.2.4_vite@6.3.6+vue@3.5.22:
    resolution: {integrity: sha512-7Yx/SXSOcQq5HiiV3orevHUFn+pmMB4cgbEkDYgnkUWb0WfeQ/wa2yFv6D5ICiCQOVpjA7vYDXrC7AGO8yjDHA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vitejs/plugin-vue/-/plugin-vue-5.2.4.tgz}
    id: registry.npmjs.org/@vitejs/plugin-vue/5.2.4
    name: '@vitejs/plugin-vue'
    version: 5.2.4
    engines: {node: ^18.0.0 || >=20.0.0}
    peerDependencies:
      vite: ^5.0.0 || ^6.0.0
      vue: ^3.2.25
    dependencies:
      vite: registry.npmjs.org/vite/6.3.6_nnwfsygotcqvs65kz7hc73f7s4
      vue: registry.npmjs.org/vue/3.5.22_typescript@5.8.3
    dev: true

  registry.npmjs.org/@volar/language-core/2.4.23:
    resolution: {integrity: sha512-hEEd5ET/oSmBC6pi1j6NaNYRWoAiDhINbT8rmwtINugR39loROSlufGdYMF9TaKGfz+ViGs1Idi3mAhnuPcoGQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@volar/language-core/-/language-core-2.4.23.tgz}
    name: '@volar/language-core'
    version: 2.4.23
    dependencies:
      '@volar/source-map': registry.npmjs.org/@volar/source-map/2.4.23
    dev: true

  registry.npmjs.org/@volar/source-map/2.4.23:
    resolution: {integrity: sha512-Z1Uc8IB57Lm6k7q6KIDu/p+JWtf3xsXJqAX/5r18hYOTpJyBn0KXUR8oTJ4WFYOcDzWC9n3IflGgHowx6U6z9Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@volar/source-map/-/source-map-2.4.23.tgz}
    name: '@volar/source-map'
    version: 2.4.23
    dev: true

  registry.npmjs.org/@volar/typescript/2.4.23:
    resolution: {integrity: sha512-lAB5zJghWxVPqfcStmAP1ZqQacMpe90UrP5RJ3arDyrhy4aCUQqmxPPLB2PWDKugvylmO41ljK7vZ+t6INMTag==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@volar/typescript/-/typescript-2.4.23.tgz}
    name: '@volar/typescript'
    version: 2.4.23
    dependencies:
      '@volar/language-core': registry.npmjs.org/@volar/language-core/2.4.23
      path-browserify: registry.npmjs.org/path-browserify/1.0.1
      vscode-uri: registry.npmjs.org/vscode-uri/3.1.0
    dev: true

  registry.npmjs.org/@vue/babel-helper-vue-transform-on/1.5.0:
    resolution: {integrity: sha512-0dAYkerNhhHutHZ34JtTl2czVQHUNWv6xEbkdF5W+Yrv5pCWsqjeORdOgbtW2I9gWlt+wBmVn+ttqN9ZxR5tzA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/babel-helper-vue-transform-on/-/babel-helper-vue-transform-on-1.5.0.tgz}
    name: '@vue/babel-helper-vue-transform-on'
    version: 1.5.0
    dev: true

  registry.npmjs.org/@vue/babel-plugin-jsx/1.5.0_@babel+core@7.28.4:
    resolution: {integrity: sha512-mneBhw1oOqCd2247O0Yw/mRwC9jIGACAJUlawkmMBiNmL4dGA2eMzuNZVNqOUfYTa6vqmND4CtOPzmEEEqLKFw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/babel-plugin-jsx/-/babel-plugin-jsx-1.5.0.tgz}
    id: registry.npmjs.org/@vue/babel-plugin-jsx/1.5.0
    name: '@vue/babel-plugin-jsx'
    version: 1.5.0
    peerDependencies:
      '@babel/core': ^7.0.0-0
    peerDependenciesMeta:
      '@babel/core':
        optional: true
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core/7.28.4
      '@babel/helper-module-imports': registry.npmjs.org/@babel/helper-module-imports/7.27.1
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils/7.27.1
      '@babel/plugin-syntax-jsx': registry.npmjs.org/@babel/plugin-syntax-jsx/7.27.1_@babel+core@7.28.4
      '@babel/template': registry.npmjs.org/@babel/template/7.27.2
      '@babel/traverse': registry.npmjs.org/@babel/traverse/7.28.4
      '@babel/types': registry.npmjs.org/@babel/types/7.28.4
      '@vue/babel-helper-vue-transform-on': registry.npmjs.org/@vue/babel-helper-vue-transform-on/1.5.0
      '@vue/babel-plugin-resolve-type': registry.npmjs.org/@vue/babel-plugin-resolve-type/1.5.0_@babel+core@7.28.4
      '@vue/shared': registry.npmjs.org/@vue/shared/3.5.22
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@vue/babel-plugin-resolve-type/1.5.0_@babel+core@7.28.4:
    resolution: {integrity: sha512-Wm/60o+53JwJODm4Knz47dxJnLDJ9FnKnGZJbUUf8nQRAtt6P+undLUAVU3Ha33LxOJe6IPoifRQ6F/0RrU31w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/babel-plugin-resolve-type/-/babel-plugin-resolve-type-1.5.0.tgz}
    id: registry.npmjs.org/@vue/babel-plugin-resolve-type/1.5.0
    name: '@vue/babel-plugin-resolve-type'
    version: 1.5.0
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/code-frame': registry.npmjs.org/@babel/code-frame/7.27.1
      '@babel/core': registry.npmjs.org/@babel/core/7.28.4
      '@babel/helper-module-imports': registry.npmjs.org/@babel/helper-module-imports/7.27.1
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils/7.27.1
      '@babel/parser': registry.npmjs.org/@babel/parser/7.28.4
      '@vue/compiler-sfc': registry.npmjs.org/@vue/compiler-sfc/3.5.22
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@vue/compiler-core/3.5.22:
    resolution: {integrity: sha512-jQ0pFPmZwTEiRNSb+i9Ow/I/cHv2tXYqsnHKKyCQ08irI2kdF5qmYedmF8si8mA7zepUFmJ2hqzS8CQmNOWOkQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.5.22.tgz}
    name: '@vue/compiler-core'
    version: 3.5.22
    dependencies:
      '@babel/parser': registry.npmjs.org/@babel/parser/7.28.4
      '@vue/shared': registry.npmjs.org/@vue/shared/3.5.22
      entities: registry.npmjs.org/entities/4.5.0
      estree-walker: registry.npmjs.org/estree-walker/2.0.2
      source-map-js: registry.npmjs.org/source-map-js/1.2.1

  registry.npmjs.org/@vue/compiler-dom/3.5.22:
    resolution: {integrity: sha512-W8RknzUM1BLkypvdz10OVsGxnMAuSIZs9Wdx1vzA3mL5fNMN15rhrSCLiTm6blWeACwUwizzPVqGJgOGBEN/hA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.5.22.tgz}
    name: '@vue/compiler-dom'
    version: 3.5.22
    dependencies:
      '@vue/compiler-core': registry.npmjs.org/@vue/compiler-core/3.5.22
      '@vue/shared': registry.npmjs.org/@vue/shared/3.5.22

  registry.npmjs.org/@vue/compiler-sfc/3.5.22:
    resolution: {integrity: sha512-tbTR1zKGce4Lj+JLzFXDq36K4vcSZbJ1RBu8FxcDv1IGRz//Dh2EBqksyGVypz3kXpshIfWKGOCcqpSbyGWRJQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.5.22.tgz}
    name: '@vue/compiler-sfc'
    version: 3.5.22
    dependencies:
      '@babel/parser': registry.npmjs.org/@babel/parser/7.28.4
      '@vue/compiler-core': registry.npmjs.org/@vue/compiler-core/3.5.22
      '@vue/compiler-dom': registry.npmjs.org/@vue/compiler-dom/3.5.22
      '@vue/compiler-ssr': registry.npmjs.org/@vue/compiler-ssr/3.5.22
      '@vue/shared': registry.npmjs.org/@vue/shared/3.5.22
      estree-walker: registry.npmjs.org/estree-walker/2.0.2
      magic-string: registry.npmjs.org/magic-string/0.30.19
      postcss: registry.npmjs.org/postcss/8.5.6
      source-map-js: registry.npmjs.org/source-map-js/1.2.1

  registry.npmjs.org/@vue/compiler-ssr/3.5.22:
    resolution: {integrity: sha512-GdgyLvg4R+7T8Nk2Mlighx7XGxq/fJf9jaVofc3IL0EPesTE86cP/8DD1lT3h1JeZr2ySBvyqKQJgbS54IX1Ww==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.5.22.tgz}
    name: '@vue/compiler-ssr'
    version: 3.5.22
    dependencies:
      '@vue/compiler-dom': registry.npmjs.org/@vue/compiler-dom/3.5.22
      '@vue/shared': registry.npmjs.org/@vue/shared/3.5.22

  registry.npmjs.org/@vue/compiler-vue2/2.7.16:
    resolution: {integrity: sha512-qYC3Psj9S/mfu9uVi5WvNZIzq+xnXMhOwbTFKKDD7b1lhpnn71jXSFdTQ+WsIEk0ONCd7VV2IMm7ONl6tbQ86A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/compiler-vue2/-/compiler-vue2-2.7.16.tgz}
    name: '@vue/compiler-vue2'
    version: 2.7.16
    dependencies:
      de-indent: registry.npmjs.org/de-indent/1.0.2
      he: registry.npmjs.org/he/1.2.0
    dev: true

  registry.npmjs.org/@vue/devtools-api/6.6.4:
    resolution: {integrity: sha512-sGhTPMuXqZ1rVOk32RylztWkfXTRhuS7vgAKv0zjqk8gbsHkJ7xfFf+jbySxt7tWObEJwyKaHMikV/WGDiQm8g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/devtools-api/-/devtools-api-6.6.4.tgz}
    name: '@vue/devtools-api'
    version: 6.6.4
    dev: false

  registry.npmjs.org/@vue/devtools-api/7.7.7:
    resolution: {integrity: sha512-lwOnNBH2e7x1fIIbVT7yF5D+YWhqELm55/4ZKf45R9T8r9dE2AIOy8HKjfqzGsoTHFbWbr337O4E0A0QADnjBg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/devtools-api/-/devtools-api-7.7.7.tgz}
    name: '@vue/devtools-api'
    version: 7.7.7
    dependencies:
      '@vue/devtools-kit': registry.npmjs.org/@vue/devtools-kit/7.7.7
    dev: false

  registry.npmjs.org/@vue/devtools-core/8.0.2_vite@6.3.6+vue@3.5.22:
    resolution: {integrity: sha512-V7eKTTHoS6KfK8PSGMLZMhGv/9yNDrmv6Qc3r71QILulnzPnqK2frsTyx3e2MrhdUZnENPEm6hcb4z0GZOqNhw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/devtools-core/-/devtools-core-8.0.2.tgz}
    id: registry.npmjs.org/@vue/devtools-core/8.0.2
    name: '@vue/devtools-core'
    version: 8.0.2
    peerDependencies:
      vue: ^3.0.0
    dependencies:
      '@vue/devtools-kit': registry.npmjs.org/@vue/devtools-kit/8.0.2
      '@vue/devtools-shared': registry.npmjs.org/@vue/devtools-shared/8.0.2
      mitt: registry.npmjs.org/mitt/3.0.1
      nanoid: registry.npmjs.org/nanoid/5.1.6
      pathe: registry.npmjs.org/pathe/2.0.3
      vite-hot-client: registry.npmjs.org/vite-hot-client/2.1.0_vite@6.3.6
      vue: registry.npmjs.org/vue/3.5.22_typescript@5.8.3
    transitivePeerDependencies:
      - vite
    dev: true

  registry.npmjs.org/@vue/devtools-kit/7.7.7:
    resolution: {integrity: sha512-wgoZtxcTta65cnZ1Q6MbAfePVFxfM+gq0saaeytoph7nEa7yMXoi6sCPy4ufO111B9msnw0VOWjPEFCXuAKRHA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/devtools-kit/-/devtools-kit-7.7.7.tgz}
    name: '@vue/devtools-kit'
    version: 7.7.7
    dependencies:
      '@vue/devtools-shared': registry.npmjs.org/@vue/devtools-shared/7.7.7
      birpc: registry.npmjs.org/birpc/2.6.1
      hookable: registry.npmjs.org/hookable/5.5.3
      mitt: registry.npmjs.org/mitt/3.0.1
      perfect-debounce: registry.npmjs.org/perfect-debounce/1.0.0
      speakingurl: registry.npmjs.org/speakingurl/14.0.1
      superjson: registry.npmjs.org/superjson/2.2.2
    dev: false

  registry.npmjs.org/@vue/devtools-kit/8.0.2:
    resolution: {integrity: sha512-yjZKdEmhJzQqbOh4KFBfTOQjDPMrjjBNCnHBvnTGJX+YLAqoUtY2J+cg7BE+EA8KUv8LprECq04ts75wCoIGWA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/devtools-kit/-/devtools-kit-8.0.2.tgz}
    name: '@vue/devtools-kit'
    version: 8.0.2
    dependencies:
      '@vue/devtools-shared': registry.npmjs.org/@vue/devtools-shared/8.0.2
      birpc: registry.npmjs.org/birpc/2.6.1
      hookable: registry.npmjs.org/hookable/5.5.3
      mitt: registry.npmjs.org/mitt/3.0.1
      perfect-debounce: registry.npmjs.org/perfect-debounce/2.0.0
      speakingurl: registry.npmjs.org/speakingurl/14.0.1
      superjson: registry.npmjs.org/superjson/2.2.2
    dev: true

  registry.npmjs.org/@vue/devtools-shared/7.7.7:
    resolution: {integrity: sha512-+udSj47aRl5aKb0memBvcUG9koarqnxNM5yjuREvqwK6T3ap4mn3Zqqc17QrBFTqSMjr3HK1cvStEZpMDpfdyw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/devtools-shared/-/devtools-shared-7.7.7.tgz}
    name: '@vue/devtools-shared'
    version: 7.7.7
    dependencies:
      rfdc: registry.npmjs.org/rfdc/1.4.1
    dev: false

  registry.npmjs.org/@vue/devtools-shared/8.0.2:
    resolution: {integrity: sha512-mLU0QVdy5Lp40PMGSixDw/Kbd6v5dkQXltd2r+mdVQV7iUog2NlZuLxFZApFZ/mObUBDhoCpf0T3zF2FWWdeHw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/devtools-shared/-/devtools-shared-8.0.2.tgz}
    name: '@vue/devtools-shared'
    version: 8.0.2
    dependencies:
      rfdc: registry.npmjs.org/rfdc/1.4.1
    dev: true

  registry.npmjs.org/@vue/eslint-config-prettier/10.2.0_m6uex47n6d4n3zhzjzmrk6xdnm:
    resolution: {integrity: sha512-GL3YBLwv/+b86yHcNNfPJxOTtVFJ4Mbc9UU3zR+KVoG7SwGTjPT+32fXamscNumElhcpXW3mT0DgzS9w32S7Bw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/eslint-config-prettier/-/eslint-config-prettier-10.2.0.tgz}
    id: registry.npmjs.org/@vue/eslint-config-prettier/10.2.0
    name: '@vue/eslint-config-prettier'
    version: 10.2.0
    peerDependencies:
      eslint: '>= 8.21.0'
      prettier: '>= 3.0.0'
    dependencies:
      eslint: registry.npmjs.org/eslint/9.36.0_jiti@2.6.0
      eslint-config-prettier: registry.npmjs.org/eslint-config-prettier/10.1.8_eslint@9.36.0
      eslint-plugin-prettier: registry.npmjs.org/eslint-plugin-prettier/5.5.4_3hufodrfa7nd3taq7p36ilfnfa
      prettier: registry.npmjs.org/prettier/3.6.2
    transitivePeerDependencies:
      - '@types/eslint'
    dev: true

  registry.npmjs.org/@vue/eslint-config-typescript/14.6.0_bak4comehp2dohjfcynw7odnqi:
    resolution: {integrity: sha512-UpiRY/7go4Yps4mYCjkvlIbVWmn9YvPGQDxTAlcKLphyaD77LjIu3plH4Y9zNT0GB4f3K5tMmhhtRhPOgrQ/bQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/eslint-config-typescript/-/eslint-config-typescript-14.6.0.tgz}
    id: registry.npmjs.org/@vue/eslint-config-typescript/14.6.0
    name: '@vue/eslint-config-typescript'
    version: 14.6.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^9.10.0
      eslint-plugin-vue: ^9.28.0 || ^10.0.0
      typescript: '>=4.8.4'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/utils': registry.npmjs.org/@typescript-eslint/utils/8.44.1_mgbfndoj365cbyr3vqehozbar4
      eslint: registry.npmjs.org/eslint/9.36.0_jiti@2.6.0
      eslint-plugin-vue: registry.npmjs.org/eslint-plugin-vue/10.3.0_eslint@9.36.0
      fast-glob: registry.npmjs.org/fast-glob/3.3.3
      typescript: registry.npmjs.org/typescript/5.8.3
      typescript-eslint: registry.npmjs.org/typescript-eslint/8.44.1_mgbfndoj365cbyr3vqehozbar4
      vue-eslint-parser: registry.npmjs.org/vue-eslint-parser/10.2.0_eslint@9.36.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@vue/language-core/3.0.8_typescript@5.8.3:
    resolution: {integrity: sha512-eYs6PF7bxoPYvek9qxceo1BCwFbJZYqJll+WaYC8o8ec60exqj+n+QRGGiJHSeUfYp0hDxARbMdxMq/fbPgU5g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/language-core/-/language-core-3.0.8.tgz}
    id: registry.npmjs.org/@vue/language-core/3.0.8
    name: '@vue/language-core'
    version: 3.0.8
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@volar/language-core': registry.npmjs.org/@volar/language-core/2.4.23
      '@vue/compiler-dom': registry.npmjs.org/@vue/compiler-dom/3.5.22
      '@vue/compiler-vue2': registry.npmjs.org/@vue/compiler-vue2/2.7.16
      '@vue/shared': registry.npmjs.org/@vue/shared/3.5.22
      alien-signals: registry.npmjs.org/alien-signals/2.0.7
      muggle-string: registry.npmjs.org/muggle-string/0.4.1
      path-browserify: registry.npmjs.org/path-browserify/1.0.1
      picomatch: registry.npmjs.org/picomatch/4.0.3
      typescript: registry.npmjs.org/typescript/5.8.3
    dev: true

  registry.npmjs.org/@vue/reactivity/3.5.22:
    resolution: {integrity: sha512-f2Wux4v/Z2pqc9+4SmgZC1p73Z53fyD90NFWXiX9AKVnVBEvLFOWCEgJD3GdGnlxPZt01PSlfmLqbLYzY/Fw4A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.5.22.tgz}
    name: '@vue/reactivity'
    version: 3.5.22
    dependencies:
      '@vue/shared': registry.npmjs.org/@vue/shared/3.5.22

  registry.npmjs.org/@vue/runtime-core/3.5.22:
    resolution: {integrity: sha512-EHo4W/eiYeAzRTN5PCextDUZ0dMs9I8mQ2Fy+OkzvRPUYQEyK9yAjbasrMCXbLNhF7P0OUyivLjIy0yc6VrLJQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/runtime-core/-/runtime-core-3.5.22.tgz}
    name: '@vue/runtime-core'
    version: 3.5.22
    dependencies:
      '@vue/reactivity': registry.npmjs.org/@vue/reactivity/3.5.22
      '@vue/shared': registry.npmjs.org/@vue/shared/3.5.22

  registry.npmjs.org/@vue/runtime-dom/3.5.22:
    resolution: {integrity: sha512-Av60jsryAkI023PlN7LsqrfPvwfxOd2yAwtReCjeuugTJTkgrksYJJstg1e12qle0NarkfhfFu1ox2D+cQotww==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/runtime-dom/-/runtime-dom-3.5.22.tgz}
    name: '@vue/runtime-dom'
    version: 3.5.22
    dependencies:
      '@vue/reactivity': registry.npmjs.org/@vue/reactivity/3.5.22
      '@vue/runtime-core': registry.npmjs.org/@vue/runtime-core/3.5.22
      '@vue/shared': registry.npmjs.org/@vue/shared/3.5.22
      csstype: registry.npmjs.org/csstype/3.1.3

  registry.npmjs.org/@vue/server-renderer/3.5.22_vue@3.5.22:
    resolution: {integrity: sha512-gXjo+ao0oHYTSswF+a3KRHZ1WszxIqO7u6XwNHqcqb9JfyIL/pbWrrh/xLv7jeDqla9u+LK7yfZKHih1e1RKAQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.5.22.tgz}
    id: registry.npmjs.org/@vue/server-renderer/3.5.22
    name: '@vue/server-renderer'
    version: 3.5.22
    peerDependencies:
      vue: 3.5.22
    dependencies:
      '@vue/compiler-ssr': registry.npmjs.org/@vue/compiler-ssr/3.5.22
      '@vue/shared': registry.npmjs.org/@vue/shared/3.5.22
      vue: registry.npmjs.org/vue/3.5.22_typescript@5.8.3

  registry.npmjs.org/@vue/shared/3.5.22:
    resolution: {integrity: sha512-F4yc6palwq3TT0u+FYf0Ns4Tfl9GRFURDN2gWG7L1ecIaS/4fCIuFOjMTnCyjsu/OK6vaDKLCrGAa+KvvH+h4w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/shared/-/shared-3.5.22.tgz}
    name: '@vue/shared'
    version: 3.5.22

  registry.npmjs.org/@vue/tsconfig/0.7.0_5w2wwtazrjreqxulnlwvyyqa5u:
    resolution: {integrity: sha512-ku2uNz5MaZ9IerPPUyOHzyjhXoX2kVJaVf7hL315DC17vS6IiZRmmCPfggNbU16QTvM80+uYYy3eYJB59WCtvg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/tsconfig/-/tsconfig-0.7.0.tgz}
    id: registry.npmjs.org/@vue/tsconfig/0.7.0
    name: '@vue/tsconfig'
    version: 0.7.0
    peerDependencies:
      typescript: 5.x
      vue: ^3.4.0
    peerDependenciesMeta:
      typescript:
        optional: true
      vue:
        optional: true
    dependencies:
      typescript: registry.npmjs.org/typescript/5.8.3
      vue: registry.npmjs.org/vue/3.5.22_typescript@5.8.3
    dev: true

  registry.npmjs.org/@vue/web-component-wrapper/1.3.0:
    resolution: {integrity: sha512-Iu8Tbg3f+emIIMmI2ycSI8QcEuAUgPTgHwesDU1eKMLE4YC/c/sFbGc70QgMq31ijRftV0R7vCm9co6rldCeOA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/@vue/web-component-wrapper/-/web-component-wrapper-1.3.0.tgz}
    name: '@vue/web-component-wrapper'
    version: 1.3.0
    dev: false

  registry.npmjs.org/acorn-jsx/5.3.2_acorn@8.15.0:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz}
    id: registry.npmjs.org/acorn-jsx/5.3.2
    name: acorn-jsx
    version: 5.3.2
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: registry.npmjs.org/acorn/8.15.0
    dev: true

  registry.npmjs.org/acorn/8.15.0:
    resolution: {integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz}
    name: acorn
    version: 8.15.0
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  registry.npmjs.org/ajv/6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz}
    name: ajv
    version: 6.12.6
    dependencies:
      fast-deep-equal: registry.npmjs.org/fast-deep-equal/3.1.3
      fast-json-stable-stringify: registry.npmjs.org/fast-json-stable-stringify/2.1.0
      json-schema-traverse: registry.npmjs.org/json-schema-traverse/0.4.1
      uri-js: registry.npmjs.org/uri-js/4.4.1
    dev: true

  registry.npmjs.org/alien-signals/2.0.7:
    resolution: {integrity: sha512-wE7y3jmYeb0+h6mr5BOovuqhFv22O/MV9j5p0ndJsa7z1zJNPGQ4ph5pQk/kTTCWRC3xsA4SmtwmkzQO+7NCNg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/alien-signals/-/alien-signals-2.0.7.tgz}
    name: alien-signals
    version: 2.0.7
    dev: true

  registry.npmjs.org/ansi-styles/4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz}
    name: ansi-styles
    version: 4.3.0
    engines: {node: '>=8'}
    dependencies:
      color-convert: registry.npmjs.org/color-convert/2.0.1
    dev: true

  registry.npmjs.org/ansi-styles/6.2.3:
    resolution: {integrity: sha512-4Dj6M28JB+oAH8kFkTLUo+a2jwOFkuqb3yucU0CANcRRUbxS0cP0nZYCGjcc3BNXwRIsUVmDGgzawme7zvJHvg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.3.tgz}
    name: ansi-styles
    version: 6.2.3
    engines: {node: '>=12'}
    dev: true

  registry.npmjs.org/ansis/4.1.0:
    resolution: {integrity: sha512-BGcItUBWSMRgOCe+SVZJ+S7yTRG0eGt9cXAHev72yuGcY23hnLA7Bky5L/xLyPINoSN95geovfBkqoTlNZYa7w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/ansis/-/ansis-4.1.0.tgz}
    name: ansis
    version: 4.1.0
    engines: {node: '>=14'}
    dev: true

  registry.npmjs.org/anymatch/3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz}
    name: anymatch
    version: 3.1.3
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: registry.npmjs.org/normalize-path/3.0.0
      picomatch: registry.npmjs.org/picomatch/2.3.1
    dev: true

  registry.npmjs.org/argparse/1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz}
    name: argparse
    version: 1.0.10
    dependencies:
      sprintf-js: registry.npmjs.org/sprintf-js/1.0.3
    dev: false

  registry.npmjs.org/argparse/2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz}
    name: argparse
    version: 2.0.1
    dev: true

  registry.npmjs.org/autolinker/3.16.2:
    resolution: {integrity: sha512-JiYl7j2Z19F9NdTmirENSUUIIL/9MytEWtmzhfmsKPCp9E+G35Y0UNCMoM9tFigxT59qSc8Ml2dlZXOCVTYwuA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/autolinker/-/autolinker-3.16.2.tgz}
    name: autolinker
    version: 3.16.2
    dependencies:
      tslib: registry.npmjs.org/tslib/2.8.1
    dev: false

  registry.npmjs.org/autoprefixer/10.4.21_postcss@8.5.6:
    resolution: {integrity: sha512-O+A6LWV5LDHSJD3LjHYoNi4VLsj/Whi7k6zG12xTYaU4cQ8oxQGckXNX8cRHK5yOZ/ppVHe0ZBXGzSV9jXdVbQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.21.tgz}
    id: registry.npmjs.org/autoprefixer/10.4.21
    name: autoprefixer
    version: 10.4.21
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      browserslist: registry.npmjs.org/browserslist/4.26.2
      caniuse-lite: registry.npmjs.org/caniuse-lite/1.0.30001745
      fraction.js: registry.npmjs.org/fraction.js/4.3.7
      normalize-range: registry.npmjs.org/normalize-range/0.1.2
      picocolors: registry.npmjs.org/picocolors/1.1.1
      postcss: registry.npmjs.org/postcss/8.5.6
      postcss-value-parser: registry.npmjs.org/postcss-value-parser/4.2.0
    dev: true

  registry.npmjs.org/balanced-match/1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz}
    name: balanced-match
    version: 1.0.2
    dev: true

  registry.npmjs.org/baseline-browser-mapping/2.8.7:
    resolution: {integrity: sha512-bxxN2M3a4d1CRoQC//IqsR5XrLh0IJ8TCv2x6Y9N0nckNz/rTjZB3//GGscZziZOxmjP55rzxg/ze7usFI9FqQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/baseline-browser-mapping/-/baseline-browser-mapping-2.8.7.tgz}
    name: baseline-browser-mapping
    version: 2.8.7
    hasBin: true
    dev: true

  registry.npmjs.org/binary-extensions/2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz}
    name: binary-extensions
    version: 2.3.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/birpc/2.6.1:
    resolution: {integrity: sha512-LPnFhlDpdSH6FJhJyn4M0kFO7vtQ5iPw24FnG0y21q09xC7e8+1LeR31S1MAIrDAHp4m7aas4bEkTDTvMAtebQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/birpc/-/birpc-2.6.1.tgz}
    name: birpc
    version: 2.6.1

  registry.npmjs.org/boolbase/1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz}
    name: boolbase
    version: 1.0.0
    dev: true

  registry.npmjs.org/brace-expansion/1.1.12:
    resolution: {integrity: sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz}
    name: brace-expansion
    version: 1.1.12
    dependencies:
      balanced-match: registry.npmjs.org/balanced-match/1.0.2
      concat-map: registry.npmjs.org/concat-map/0.0.1
    dev: true

  registry.npmjs.org/brace-expansion/2.0.2:
    resolution: {integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz}
    name: brace-expansion
    version: 2.0.2
    dependencies:
      balanced-match: registry.npmjs.org/balanced-match/1.0.2
    dev: true

  registry.npmjs.org/braces/3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/braces/-/braces-3.0.3.tgz}
    name: braces
    version: 3.0.3
    engines: {node: '>=8'}
    dependencies:
      fill-range: registry.npmjs.org/fill-range/7.1.1
    dev: true

  registry.npmjs.org/browserslist/4.26.2:
    resolution: {integrity: sha512-ECFzp6uFOSB+dcZ5BK/IBaGWssbSYBHvuMeMt3MMFyhI0Z8SqGgEkBLARgpRH3hutIgPVsALcMwbDrJqPxQ65A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/browserslist/-/browserslist-4.26.2.tgz}
    name: browserslist
    version: 4.26.2
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      baseline-browser-mapping: registry.npmjs.org/baseline-browser-mapping/2.8.7
      caniuse-lite: registry.npmjs.org/caniuse-lite/1.0.30001745
      electron-to-chromium: registry.npmjs.org/electron-to-chromium/1.5.224
      node-releases: registry.npmjs.org/node-releases/2.0.21
      update-browserslist-db: registry.npmjs.org/update-browserslist-db/1.1.3_browserslist@4.26.2
    dev: true

  registry.npmjs.org/bundle-name/4.1.0:
    resolution: {integrity: sha512-tjwM5exMg6BGRI+kNmTntNsvdZS1X8BFYS6tnJ2hdH0kVxM6/eVZ2xy+FqStSWvYmtfFMDLIxurorHwDKfDz5Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/bundle-name/-/bundle-name-4.1.0.tgz}
    name: bundle-name
    version: 4.1.0
    engines: {node: '>=18'}
    dependencies:
      run-applescript: registry.npmjs.org/run-applescript/7.1.0
    dev: true

  registry.npmjs.org/cac/6.7.14:
    resolution: {integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/cac/-/cac-6.7.14.tgz}
    name: cac
    version: 6.7.14
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/callsites/3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz}
    name: callsites
    version: 3.1.0
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/caniuse-lite/1.0.30001745:
    resolution: {integrity: sha512-ywt6i8FzvdgrrrGbr1jZVObnVv6adj+0if2/omv9cmR2oiZs30zL4DIyaptKcbOrBdOIc74QTMoJvSE2QHh5UQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001745.tgz}
    name: caniuse-lite
    version: 1.0.30001745
    dev: true

  registry.npmjs.org/chalk/4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz}
    name: chalk
    version: 4.1.2
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: registry.npmjs.org/ansi-styles/4.3.0
      supports-color: registry.npmjs.org/supports-color/7.2.0
    dev: true

  registry.npmjs.org/chokidar/3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz}
    name: chokidar
    version: 3.6.0
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: registry.npmjs.org/anymatch/3.1.3
      braces: registry.npmjs.org/braces/3.0.3
      glob-parent: registry.npmjs.org/glob-parent/5.1.2
      is-binary-path: registry.npmjs.org/is-binary-path/2.1.0
      is-glob: registry.npmjs.org/is-glob/4.0.3
      normalize-path: registry.npmjs.org/normalize-path/3.0.0
      readdirp: registry.npmjs.org/readdirp/3.6.0
    optionalDependencies:
      fsevents: registry.npmjs.org/fsevents/2.3.3
    dev: true

  registry.npmjs.org/chokidar/4.0.3:
    resolution: {integrity: sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/chokidar/-/chokidar-4.0.3.tgz}
    name: chokidar
    version: 4.0.3
    engines: {node: '>= 14.16.0'}
    dependencies:
      readdirp: registry.npmjs.org/readdirp/4.1.2
    dev: true

  registry.npmjs.org/color-convert/2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz}
    name: color-convert
    version: 2.0.1
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: registry.npmjs.org/color-name/1.1.4
    dev: true

  registry.npmjs.org/color-name/1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz}
    name: color-name
    version: 1.1.4
    dev: true

  registry.npmjs.org/colorette/2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz}
    name: colorette
    version: 2.0.20
    dev: true

  registry.npmjs.org/concat-map/0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz}
    name: concat-map
    version: 0.0.1
    dev: true

  registry.npmjs.org/confbox/0.1.8:
    resolution: {integrity: sha512-RMtmw0iFkeR4YV+fUOSucriAQNb9g8zFR52MWCtl+cCZOFRNL6zeB395vPzFhEjjn4fMxXudmELnl/KF/WrK6w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/confbox/-/confbox-0.1.8.tgz}
    name: confbox
    version: 0.1.8
    dev: true

  registry.npmjs.org/confbox/0.2.2:
    resolution: {integrity: sha512-1NB+BKqhtNipMsov4xI/NnhCKp9XG9NamYp5PVm9klAT0fsrNPjaFICsCFhNhwZJKNh7zB/3q8qXz0E9oaMNtQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/confbox/-/confbox-0.2.2.tgz}
    name: confbox
    version: 0.2.2
    dev: true

  registry.npmjs.org/consola/3.4.2:
    resolution: {integrity: sha512-5IKcdX0nnYavi6G7TtOhwkYzyjfJlatbjMjuLSfE2kYT5pMDOilZ4OvMhi637CcDICTmz3wARPoyhqyX1Y+XvA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/consola/-/consola-3.4.2.tgz}
    name: consola
    version: 3.4.2
    engines: {node: ^14.18.0 || >=16.10.0}
    dev: true

  registry.npmjs.org/convert-source-map/2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz}
    name: convert-source-map
    version: 2.0.0
    dev: true

  registry.npmjs.org/copy-anything/3.0.5:
    resolution: {integrity: sha512-yCEafptTtb4bk7GLEQoM8KVJpxAfdBJYaXyzQEgQQQgYrZiDp8SJmGKlYza6CYjEDNstAdNdKA3UuoULlEbS6w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/copy-anything/-/copy-anything-3.0.5.tgz}
    name: copy-anything
    version: 3.0.5
    engines: {node: '>=12.13'}
    dependencies:
      is-what: registry.npmjs.org/is-what/4.1.16

  registry.npmjs.org/cross-spawn/7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz}
    name: cross-spawn
    version: 7.0.6
    engines: {node: '>= 8'}
    dependencies:
      path-key: registry.npmjs.org/path-key/3.1.1
      shebang-command: registry.npmjs.org/shebang-command/2.0.0
      which: registry.npmjs.org/which/2.0.2
    dev: true

  registry.npmjs.org/css-tree/3.1.0:
    resolution: {integrity: sha512-0eW44TGN5SQXU1mWSkKwFstI/22X2bG1nYzZTYMAWjylYURhse752YgbE4Cx46AC+bAvI+/dYTPRk1LqSUnu6w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/css-tree/-/css-tree-3.1.0.tgz}
    name: css-tree
    version: 3.1.0
    engines: {node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0}
    dependencies:
      mdn-data: registry.npmjs.org/mdn-data/2.12.2
      source-map-js: registry.npmjs.org/source-map-js/1.2.1
    dev: true

  registry.npmjs.org/cssesc/3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz}
    name: cssesc
    version: 3.0.0
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  registry.npmjs.org/csstype/3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz}
    name: csstype
    version: 3.1.3

  registry.npmjs.org/de-indent/1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/de-indent/-/de-indent-1.0.2.tgz}
    name: de-indent
    version: 1.0.2
    dev: true

  registry.npmjs.org/debug/4.4.3:
    resolution: {integrity: sha512-RGwwWnwQvkVfavKVt22FGLw+xYSdzARwm0ru6DhTVA3umU5hZc28V3kO4stgYryrTlLpuvgI9GiijltAjNbcqA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/debug/-/debug-4.4.3.tgz}
    name: debug
    version: 4.4.3
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: registry.npmjs.org/ms/2.1.3
    dev: true

  registry.npmjs.org/deep-chat/2.2.2:
    resolution: {integrity: sha512-2e/VqEsYsRAPG64SB6ieResgVk3TW/a0qQuNY/gAakCRKXnEXeWoU8RPNQRHTJUp/LXU2Uo7uiqcBvVsVRdtGA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/deep-chat/-/deep-chat-2.2.2.tgz}
    name: deep-chat
    version: 2.2.2
    dependencies:
      '@microsoft/fetch-event-source': registry.npmjs.org/@microsoft/fetch-event-source/2.0.1
      remarkable: registry.npmjs.org/remarkable/2.0.1
      speech-to-element: registry.npmjs.org/speech-to-element/1.0.4
    dev: false

  registry.npmjs.org/deep-is/0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz}
    name: deep-is
    version: 0.1.4
    dev: true

  registry.npmjs.org/default-browser-id/5.0.0:
    resolution: {integrity: sha512-A6p/pu/6fyBcA1TRz/GqWYPViplrftcW2gZC9q79ngNCKAeR/X3gcEdXQHl4KNXV+3wgIJ1CPkJQ3IHM6lcsyA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/default-browser-id/-/default-browser-id-5.0.0.tgz}
    name: default-browser-id
    version: 5.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmjs.org/default-browser/5.2.1:
    resolution: {integrity: sha512-WY/3TUME0x3KPYdRRxEJJvXRHV4PyPoUsxtZa78lwItwRQRHhd2U9xOscaT/YTf8uCXIAjeJOFBVEh/7FtD8Xg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/default-browser/-/default-browser-5.2.1.tgz}
    name: default-browser
    version: 5.2.1
    engines: {node: '>=18'}
    dependencies:
      bundle-name: registry.npmjs.org/bundle-name/4.1.0
      default-browser-id: registry.npmjs.org/default-browser-id/5.0.0
    dev: true

  registry.npmjs.org/define-lazy-prop/3.0.0:
    resolution: {integrity: sha512-N+MeXYoqr3pOgn8xfyRPREN7gHakLYjhsHhWGT3fWAiL4IkAt0iDw14QiiEm2bE30c5XX5q0FtAA3CK5f9/BUg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-3.0.0.tgz}
    name: define-lazy-prop
    version: 3.0.0
    engines: {node: '>=12'}
    dev: true

  registry.npmjs.org/defu/6.1.4:
    resolution: {integrity: sha512-mEQCMmwJu317oSz8CwdIOdwf3xMif1ttiM8LTufzc3g6kR+9Pe236twL8j3IYT1F7GfRgGcW6MWxzZjLIkuHIg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/defu/-/defu-6.1.4.tgz}
    name: defu
    version: 6.1.4
    dev: true

  registry.npmjs.org/destr/2.0.5:
    resolution: {integrity: sha512-ugFTXCtDZunbzasqBxrK93Ik/DRYsO6S/fedkWEMKqt04xZ4csmnmwGDBAb07QWNaGMAmnTIemsYZCksjATwsA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/destr/-/destr-2.0.5.tgz}
    name: destr
    version: 2.0.5
    dev: true

  registry.npmjs.org/detect-libc/1.0.3:
    resolution: {integrity: sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz}
    name: detect-libc
    version: 1.0.3
    engines: {node: '>=0.10'}
    hasBin: true
    dev: true
    optional: true

  registry.npmjs.org/duplexer/0.1.2:
    resolution: {integrity: sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/duplexer/-/duplexer-0.1.2.tgz}
    name: duplexer
    version: 0.1.2
    dev: true

  registry.npmjs.org/electron-to-chromium/1.5.224:
    resolution: {integrity: sha512-kWAoUu/bwzvnhpdZSIc6KUyvkI1rbRXMT0Eq8pKReyOyaPZcctMli+EgvcN1PAvwVc7Tdo4Fxi2PsLNDU05mdg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.224.tgz}
    name: electron-to-chromium
    version: 1.5.224
    dev: true

  registry.npmjs.org/entities/4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/entities/-/entities-4.5.0.tgz}
    name: entities
    version: 4.5.0
    engines: {node: '>=0.12'}

  registry.npmjs.org/error-stack-parser-es/1.0.5:
    resolution: {integrity: sha512-5qucVt2XcuGMcEGgWI7i+yZpmpByQ8J1lHhcL7PwqCwu9FPP3VUXzT4ltHe5i2z9dePwEHcDVOAfSnHsOlCXRA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/error-stack-parser-es/-/error-stack-parser-es-1.0.5.tgz}
    name: error-stack-parser-es
    version: 1.0.5
    dev: true

  registry.npmjs.org/esbuild/0.25.10:
    resolution: {integrity: sha512-9RiGKvCwaqxO2owP61uQ4BgNborAQskMR6QusfWzQqv7AZOg5oGehdY2pRJMTKuwxd1IDBP4rSbI5lHzU7SMsQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/esbuild/-/esbuild-0.25.10.tgz}
    name: esbuild
    version: 0.25.10
    engines: {node: '>=18'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/aix-ppc64': registry.npmjs.org/@esbuild/aix-ppc64/0.25.10
      '@esbuild/android-arm': registry.npmjs.org/@esbuild/android-arm/0.25.10
      '@esbuild/android-arm64': registry.npmjs.org/@esbuild/android-arm64/0.25.10
      '@esbuild/android-x64': registry.npmjs.org/@esbuild/android-x64/0.25.10
      '@esbuild/darwin-arm64': registry.npmjs.org/@esbuild/darwin-arm64/0.25.10
      '@esbuild/darwin-x64': registry.npmjs.org/@esbuild/darwin-x64/0.25.10
      '@esbuild/freebsd-arm64': registry.npmjs.org/@esbuild/freebsd-arm64/0.25.10
      '@esbuild/freebsd-x64': registry.npmjs.org/@esbuild/freebsd-x64/0.25.10
      '@esbuild/linux-arm': registry.npmjs.org/@esbuild/linux-arm/0.25.10
      '@esbuild/linux-arm64': registry.npmjs.org/@esbuild/linux-arm64/0.25.10
      '@esbuild/linux-ia32': registry.npmjs.org/@esbuild/linux-ia32/0.25.10
      '@esbuild/linux-loong64': registry.npmjs.org/@esbuild/linux-loong64/0.25.10
      '@esbuild/linux-mips64el': registry.npmjs.org/@esbuild/linux-mips64el/0.25.10
      '@esbuild/linux-ppc64': registry.npmjs.org/@esbuild/linux-ppc64/0.25.10
      '@esbuild/linux-riscv64': registry.npmjs.org/@esbuild/linux-riscv64/0.25.10
      '@esbuild/linux-s390x': registry.npmjs.org/@esbuild/linux-s390x/0.25.10
      '@esbuild/linux-x64': registry.npmjs.org/@esbuild/linux-x64/0.25.10
      '@esbuild/netbsd-arm64': registry.npmjs.org/@esbuild/netbsd-arm64/0.25.10
      '@esbuild/netbsd-x64': registry.npmjs.org/@esbuild/netbsd-x64/0.25.10
      '@esbuild/openbsd-arm64': registry.npmjs.org/@esbuild/openbsd-arm64/0.25.10
      '@esbuild/openbsd-x64': registry.npmjs.org/@esbuild/openbsd-x64/0.25.10
      '@esbuild/openharmony-arm64': registry.npmjs.org/@esbuild/openharmony-arm64/0.25.10
      '@esbuild/sunos-x64': registry.npmjs.org/@esbuild/sunos-x64/0.25.10
      '@esbuild/win32-arm64': registry.npmjs.org/@esbuild/win32-arm64/0.25.10
      '@esbuild/win32-ia32': registry.npmjs.org/@esbuild/win32-ia32/0.25.10
      '@esbuild/win32-x64': registry.npmjs.org/@esbuild/win32-x64/0.25.10
    dev: true

  registry.npmjs.org/escalade/3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz}
    name: escalade
    version: 3.2.0
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/escape-string-regexp/4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz}
    name: escape-string-regexp
    version: 4.0.0
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/eslint-config-prettier/10.1.8_eslint@9.36.0:
    resolution: {integrity: sha512-82GZUjRS0p/jganf6q1rEO25VSoHH0hKPCTrgillPjdI/3bgBhAE1QzHrHTizjpRvy6pGAvKjDJtk2pF9NDq8w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-10.1.8.tgz}
    id: registry.npmjs.org/eslint-config-prettier/10.1.8
    name: eslint-config-prettier
    version: 10.1.8
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'
    dependencies:
      eslint: registry.npmjs.org/eslint/9.36.0_jiti@2.6.0
    dev: true

  registry.npmjs.org/eslint-plugin-prettier/5.5.4_3hufodrfa7nd3taq7p36ilfnfa:
    resolution: {integrity: sha512-swNtI95SToIz05YINMA6Ox5R057IMAmWZ26GqPxusAp1TZzj+IdY9tXNWWD3vkF/wEqydCONcwjTFpxybBqZsg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/eslint-plugin-prettier/-/eslint-plugin-prettier-5.5.4.tgz}
    id: registry.npmjs.org/eslint-plugin-prettier/5.5.4
    name: eslint-plugin-prettier
    version: 5.5.4
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      '@types/eslint': '>=8.0.0'
      eslint: '>=8.0.0'
      eslint-config-prettier: '>= 7.0.0 <10.0.0 || >=10.1.0'
      prettier: '>=3.0.0'
    peerDependenciesMeta:
      '@types/eslint':
        optional: true
      eslint-config-prettier:
        optional: true
    dependencies:
      eslint: registry.npmjs.org/eslint/9.36.0_jiti@2.6.0
      eslint-config-prettier: registry.npmjs.org/eslint-config-prettier/10.1.8_eslint@9.36.0
      prettier: registry.npmjs.org/prettier/3.6.2
      prettier-linter-helpers: registry.npmjs.org/prettier-linter-helpers/1.0.0
      synckit: registry.npmjs.org/synckit/0.11.11
    dev: true

  registry.npmjs.org/eslint-plugin-vue/10.3.0_eslint@9.36.0:
    resolution: {integrity: sha512-A0u9snqjCfYaPnqqOaH6MBLVWDUIN4trXn8J3x67uDcXvR7X6Ut8p16N+nYhMCQ9Y7edg2BIRGzfyZsY0IdqoQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/eslint-plugin-vue/-/eslint-plugin-vue-10.3.0.tgz}
    id: registry.npmjs.org/eslint-plugin-vue/10.3.0
    name: eslint-plugin-vue
    version: 10.3.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^7.0.0 || ^8.0.0
      eslint: ^8.57.0 || ^9.0.0
      vue-eslint-parser: ^10.0.0
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
    dependencies:
      '@eslint-community/eslint-utils': registry.npmjs.org/@eslint-community/eslint-utils/4.9.0_eslint@9.36.0
      eslint: registry.npmjs.org/eslint/9.36.0_jiti@2.6.0
      natural-compare: registry.npmjs.org/natural-compare/1.4.0
      nth-check: registry.npmjs.org/nth-check/2.1.1
      postcss-selector-parser: registry.npmjs.org/postcss-selector-parser/6.1.2
      semver: registry.npmjs.org/semver/7.7.2
      xml-name-validator: registry.npmjs.org/xml-name-validator/4.0.0
    dev: true

  registry.npmjs.org/eslint-scope/8.4.0:
    resolution: {integrity: sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.4.0.tgz}
    name: eslint-scope
    version: 8.4.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      esrecurse: registry.npmjs.org/esrecurse/4.3.0
      estraverse: registry.npmjs.org/estraverse/5.3.0
    dev: true

  registry.npmjs.org/eslint-visitor-keys/3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz}
    name: eslint-visitor-keys
    version: 3.4.3
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  registry.npmjs.org/eslint-visitor-keys/4.2.1:
    resolution: {integrity: sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz}
    name: eslint-visitor-keys
    version: 4.2.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dev: true

  registry.npmjs.org/eslint/9.36.0_jiti@2.6.0:
    resolution: {integrity: sha512-hB4FIzXovouYzwzECDcUkJ4OcfOEkXTv2zRY6B9bkwjx/cprAq0uvm1nl7zvQ0/TsUk0zQiN4uPfJpB9m+rPMQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/eslint/-/eslint-9.36.0.tgz}
    id: registry.npmjs.org/eslint/9.36.0
    name: eslint
    version: 9.36.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true
    dependencies:
      '@eslint-community/eslint-utils': registry.npmjs.org/@eslint-community/eslint-utils/4.9.0_eslint@9.36.0
      '@eslint-community/regexpp': registry.npmjs.org/@eslint-community/regexpp/4.12.1
      '@eslint/config-array': registry.npmjs.org/@eslint/config-array/0.21.0
      '@eslint/config-helpers': registry.npmjs.org/@eslint/config-helpers/0.3.1
      '@eslint/core': registry.npmjs.org/@eslint/core/0.15.2
      '@eslint/eslintrc': registry.npmjs.org/@eslint/eslintrc/3.3.1
      '@eslint/js': registry.npmjs.org/@eslint/js/9.36.0
      '@eslint/plugin-kit': registry.npmjs.org/@eslint/plugin-kit/0.3.5
      '@humanfs/node': registry.npmjs.org/@humanfs/node/0.16.7
      '@humanwhocodes/module-importer': registry.npmjs.org/@humanwhocodes/module-importer/1.0.1
      '@humanwhocodes/retry': registry.npmjs.org/@humanwhocodes/retry/0.4.3
      '@types/estree': registry.npmjs.org/@types/estree/1.0.8
      '@types/json-schema': registry.npmjs.org/@types/json-schema/7.0.15
      ajv: registry.npmjs.org/ajv/6.12.6
      chalk: registry.npmjs.org/chalk/4.1.2
      cross-spawn: registry.npmjs.org/cross-spawn/7.0.6
      debug: registry.npmjs.org/debug/4.4.3
      escape-string-regexp: registry.npmjs.org/escape-string-regexp/4.0.0
      eslint-scope: registry.npmjs.org/eslint-scope/8.4.0
      eslint-visitor-keys: registry.npmjs.org/eslint-visitor-keys/4.2.1
      espree: registry.npmjs.org/espree/10.4.0
      esquery: registry.npmjs.org/esquery/1.6.0
      esutils: registry.npmjs.org/esutils/2.0.3
      fast-deep-equal: registry.npmjs.org/fast-deep-equal/3.1.3
      file-entry-cache: registry.npmjs.org/file-entry-cache/8.0.0
      find-up: registry.npmjs.org/find-up/5.0.0
      glob-parent: registry.npmjs.org/glob-parent/6.0.2
      ignore: registry.npmjs.org/ignore/5.3.2
      imurmurhash: registry.npmjs.org/imurmurhash/0.1.4
      is-glob: registry.npmjs.org/is-glob/4.0.3
      jiti: registry.npmjs.org/jiti/2.6.0
      json-stable-stringify-without-jsonify: registry.npmjs.org/json-stable-stringify-without-jsonify/1.0.1
      lodash.merge: registry.npmjs.org/lodash.merge/4.6.2
      minimatch: registry.npmjs.org/minimatch/3.1.2
      natural-compare: registry.npmjs.org/natural-compare/1.4.0
      optionator: registry.npmjs.org/optionator/0.9.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/espree/10.4.0:
    resolution: {integrity: sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/espree/-/espree-10.4.0.tgz}
    name: espree
    version: 10.4.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    dependencies:
      acorn: registry.npmjs.org/acorn/8.15.0
      acorn-jsx: registry.npmjs.org/acorn-jsx/5.3.2_acorn@8.15.0
      eslint-visitor-keys: registry.npmjs.org/eslint-visitor-keys/4.2.1
    dev: true

  registry.npmjs.org/esquery/1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz}
    name: esquery
    version: 1.6.0
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: registry.npmjs.org/estraverse/5.3.0
    dev: true

  registry.npmjs.org/esrecurse/4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz}
    name: esrecurse
    version: 4.3.0
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: registry.npmjs.org/estraverse/5.3.0
    dev: true

  registry.npmjs.org/estraverse/5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz}
    name: estraverse
    version: 5.3.0
    engines: {node: '>=4.0'}
    dev: true

  registry.npmjs.org/estree-walker/2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz}
    name: estree-walker
    version: 2.0.2

  registry.npmjs.org/esutils/2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz}
    name: esutils
    version: 2.0.3
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/execa/9.6.0:
    resolution: {integrity: sha512-jpWzZ1ZhwUmeWRhS7Qv3mhpOhLfwI+uAX4e5fOcXqwMR7EcJ0pj2kV1CVzHVMX/LphnKWD3LObjZCoJ71lKpHw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/execa/-/execa-9.6.0.tgz}
    name: execa
    version: 9.6.0
    engines: {node: ^18.19.0 || >=20.5.0}
    dependencies:
      '@sindresorhus/merge-streams': registry.npmjs.org/@sindresorhus/merge-streams/4.0.0
      cross-spawn: registry.npmjs.org/cross-spawn/7.0.6
      figures: registry.npmjs.org/figures/6.1.0
      get-stream: registry.npmjs.org/get-stream/9.0.1
      human-signals: registry.npmjs.org/human-signals/8.0.1
      is-plain-obj: registry.npmjs.org/is-plain-obj/4.1.0
      is-stream: registry.npmjs.org/is-stream/4.0.1
      npm-run-path: registry.npmjs.org/npm-run-path/6.0.0
      pretty-ms: registry.npmjs.org/pretty-ms/9.3.0
      signal-exit: registry.npmjs.org/signal-exit/4.1.0
      strip-final-newline: registry.npmjs.org/strip-final-newline/4.0.0
      yoctocolors: registry.npmjs.org/yoctocolors/2.1.2
    dev: true

  registry.npmjs.org/exsolve/1.0.7:
    resolution: {integrity: sha512-VO5fQUzZtI6C+vx4w/4BWJpg3s/5l+6pRQEHzFRM8WFi4XffSP1Z+4qi7GbjWbvRQEbdIco5mIMq+zX4rPuLrw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/exsolve/-/exsolve-1.0.7.tgz}
    name: exsolve
    version: 1.0.7
    dev: true

  registry.npmjs.org/fast-deep-equal/3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz}
    name: fast-deep-equal
    version: 3.1.3
    dev: true

  registry.npmjs.org/fast-diff/1.3.0:
    resolution: {integrity: sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/fast-diff/-/fast-diff-1.3.0.tgz}
    name: fast-diff
    version: 1.3.0
    dev: true

  registry.npmjs.org/fast-glob/3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz}
    name: fast-glob
    version: 3.3.3
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': registry.npmjs.org/@nodelib/fs.stat/2.0.5
      '@nodelib/fs.walk': registry.npmjs.org/@nodelib/fs.walk/1.2.8
      glob-parent: registry.npmjs.org/glob-parent/5.1.2
      merge2: registry.npmjs.org/merge2/1.4.1
      micromatch: registry.npmjs.org/micromatch/4.0.8
    dev: true

  registry.npmjs.org/fast-json-stable-stringify/2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz}
    name: fast-json-stable-stringify
    version: 2.1.0
    dev: true

  registry.npmjs.org/fast-levenshtein/2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz}
    name: fast-levenshtein
    version: 2.0.6
    dev: true

  registry.npmjs.org/fastq/1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz}
    name: fastq
    version: 1.19.1
    dependencies:
      reusify: registry.npmjs.org/reusify/1.1.0
    dev: true

  registry.npmjs.org/fdir/6.5.0_picomatch@4.0.3:
    resolution: {integrity: sha512-tIbYtZbucOs0BRGqPJkshJUYdL+SDH7dVM8gjy+ERp3WAUjLEFJE+02kanyHtwjWOnwrKYBiwAmM0p4kLJAnXg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/fdir/-/fdir-6.5.0.tgz}
    id: registry.npmjs.org/fdir/6.5.0
    name: fdir
    version: 6.5.0
    engines: {node: '>=12.0.0'}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true
    dependencies:
      picomatch: registry.npmjs.org/picomatch/4.0.3
    dev: true

  registry.npmjs.org/figures/6.1.0:
    resolution: {integrity: sha512-d+l3qxjSesT4V7v2fh+QnmFnUWv9lSpjarhShNTgBOfA0ttejbQUAlHLitbjkoRiDulW0OPoQPYIGhIC8ohejg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/figures/-/figures-6.1.0.tgz}
    name: figures
    version: 6.1.0
    engines: {node: '>=18'}
    dependencies:
      is-unicode-supported: registry.npmjs.org/is-unicode-supported/2.1.0
    dev: true

  registry.npmjs.org/file-entry-cache/8.0.0:
    resolution: {integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-8.0.0.tgz}
    name: file-entry-cache
    version: 8.0.0
    engines: {node: '>=16.0.0'}
    dependencies:
      flat-cache: registry.npmjs.org/flat-cache/4.0.1
    dev: true

  registry.npmjs.org/fill-range/7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz}
    name: fill-range
    version: 7.1.1
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: registry.npmjs.org/to-regex-range/5.0.1
    dev: true

  registry.npmjs.org/find-up/5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz}
    name: find-up
    version: 5.0.0
    engines: {node: '>=10'}
    dependencies:
      locate-path: registry.npmjs.org/locate-path/6.0.0
      path-exists: registry.npmjs.org/path-exists/4.0.0
    dev: true

  registry.npmjs.org/flat-cache/4.0.1:
    resolution: {integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/flat-cache/-/flat-cache-4.0.1.tgz}
    name: flat-cache
    version: 4.0.1
    engines: {node: '>=16'}
    dependencies:
      flatted: registry.npmjs.org/flatted/3.3.3
      keyv: registry.npmjs.org/keyv/4.5.4
    dev: true

  registry.npmjs.org/flatted/3.3.3:
    resolution: {integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz}
    name: flatted
    version: 3.3.3
    dev: true

  registry.npmjs.org/fraction.js/4.3.7:
    resolution: {integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz}
    name: fraction.js
    version: 4.3.7
    dev: true

  registry.npmjs.org/fsevents/2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz}
    name: fsevents
    version: 2.3.3
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/gensync/1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz}
    name: gensync
    version: 1.0.0-beta.2
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmjs.org/get-stream/9.0.1:
    resolution: {integrity: sha512-kVCxPF3vQM/N0B1PmoqVUqgHP+EeVjmZSQn+1oCRPxd2P21P2F19lIgbR3HBosbB1PUhOAoctJnfEn2GbN2eZA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/get-stream/-/get-stream-9.0.1.tgz}
    name: get-stream
    version: 9.0.1
    engines: {node: '>=18'}
    dependencies:
      '@sec-ant/readable-stream': registry.npmjs.org/@sec-ant/readable-stream/0.4.1
      is-stream: registry.npmjs.org/is-stream/4.0.1
    dev: true

  registry.npmjs.org/glob-parent/5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz}
    name: glob-parent
    version: 5.1.2
    engines: {node: '>= 6'}
    dependencies:
      is-glob: registry.npmjs.org/is-glob/4.0.3
    dev: true

  registry.npmjs.org/glob-parent/6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz}
    name: glob-parent
    version: 6.0.2
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: registry.npmjs.org/is-glob/4.0.3
    dev: true

  registry.npmjs.org/globals/11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/globals/-/globals-11.12.0.tgz}
    name: globals
    version: 11.12.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/globals/14.0.0:
    resolution: {integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/globals/-/globals-14.0.0.tgz}
    name: globals
    version: 14.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmjs.org/globals/15.15.0:
    resolution: {integrity: sha512-7ACyT3wmyp3I61S4fG682L0VA2RGD9otkqGJIwNUMF1SWUombIIk+af1unuDYgMm082aHYwD+mzJvv9Iu8dsgg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/globals/-/globals-15.15.0.tgz}
    name: globals
    version: 15.15.0
    engines: {node: '>=18'}
    dev: true

  registry.npmjs.org/graphemer/1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz}
    name: graphemer
    version: 1.4.0
    dev: true

  registry.npmjs.org/gzip-size/6.0.0:
    resolution: {integrity: sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/gzip-size/-/gzip-size-6.0.0.tgz}
    name: gzip-size
    version: 6.0.0
    engines: {node: '>=10'}
    dependencies:
      duplexer: registry.npmjs.org/duplexer/0.1.2
    dev: true

  registry.npmjs.org/has-flag/4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz}
    name: has-flag
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/he/1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/he/-/he-1.2.0.tgz}
    name: he
    version: 1.2.0
    hasBin: true
    dev: true

  registry.npmjs.org/hookable/5.5.3:
    resolution: {integrity: sha512-Yc+BQe8SvoXH1643Qez1zqLRmbA5rCL+sSmk6TVos0LWVfNIB7PGncdlId77WzLGSIB5KaWgTaNTs2lNVEI6VQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/hookable/-/hookable-5.5.3.tgz}
    name: hookable
    version: 5.5.3

  registry.npmjs.org/human-signals/8.0.1:
    resolution: {integrity: sha512-eKCa6bwnJhvxj14kZk5NCPc6Hb6BdsU9DZcOnmQKSnO1VKrfV0zCvtttPZUsBvjmNDn8rpcJfpwSYnHBjc95MQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/human-signals/-/human-signals-8.0.1.tgz}
    name: human-signals
    version: 8.0.1
    engines: {node: '>=18.18.0'}
    dev: true

  registry.npmjs.org/ignore/5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz}
    name: ignore
    version: 5.3.2
    engines: {node: '>= 4'}
    dev: true

  registry.npmjs.org/ignore/7.0.5:
    resolution: {integrity: sha512-Hs59xBNfUIunMFgWAbGX5cq6893IbWg4KnrjbYwX3tx0ztorVgTDA6B2sxf8ejHJ4wz8BqGUMYlnzNBer5NvGg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/ignore/-/ignore-7.0.5.tgz}
    name: ignore
    version: 7.0.5
    engines: {node: '>= 4'}
    dev: true

  registry.npmjs.org/immutable/5.1.3:
    resolution: {integrity: sha512-+chQdDfvscSF1SJqv2gn4SRO2ZyS3xL3r7IW/wWEEzrzLisnOlKiQu5ytC/BVNcS15C39WT2Hg/bjKjDMcu+zg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/immutable/-/immutable-5.1.3.tgz}
    name: immutable
    version: 5.1.3
    dev: true

  registry.npmjs.org/import-fresh/3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz}
    name: import-fresh
    version: 3.3.1
    engines: {node: '>=6'}
    dependencies:
      parent-module: registry.npmjs.org/parent-module/1.0.1
      resolve-from: registry.npmjs.org/resolve-from/4.0.0
    dev: true

  registry.npmjs.org/imurmurhash/0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz}
    name: imurmurhash
    version: 0.1.4
    engines: {node: '>=0.8.19'}
    dev: true

  registry.npmjs.org/is-binary-path/2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz}
    name: is-binary-path
    version: 2.1.0
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: registry.npmjs.org/binary-extensions/2.3.0
    dev: true

  registry.npmjs.org/is-docker/3.0.0:
    resolution: {integrity: sha512-eljcgEDlEns/7AXFosB5K/2nCM4P7FQPkGc/DWLy5rmFEWvZayGrik1d9/QIY5nJ4f9YsVvBkA6kJpHn9rISdQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/is-docker/-/is-docker-3.0.0.tgz}
    name: is-docker
    version: 3.0.0
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    hasBin: true
    dev: true

  registry.npmjs.org/is-extglob/2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz}
    name: is-extglob
    version: 2.1.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/is-glob/4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz}
    name: is-glob
    version: 4.0.3
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: registry.npmjs.org/is-extglob/2.1.1
    dev: true

  registry.npmjs.org/is-inside-container/1.0.0:
    resolution: {integrity: sha512-KIYLCCJghfHZxqjYBE7rEy0OBuTd5xCHS7tHVgvCLkx7StIoaxwNW3hCALgEUjFfeRk+MG/Qxmp/vtETEF3tRA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/is-inside-container/-/is-inside-container-1.0.0.tgz}
    name: is-inside-container
    version: 1.0.0
    engines: {node: '>=14.16'}
    hasBin: true
    dependencies:
      is-docker: registry.npmjs.org/is-docker/3.0.0
    dev: true

  registry.npmjs.org/is-number/7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz}
    name: is-number
    version: 7.0.0
    engines: {node: '>=0.12.0'}
    dev: true

  registry.npmjs.org/is-plain-obj/4.1.0:
    resolution: {integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-4.1.0.tgz}
    name: is-plain-obj
    version: 4.1.0
    engines: {node: '>=12'}
    dev: true

  registry.npmjs.org/is-stream/4.0.1:
    resolution: {integrity: sha512-Dnz92NInDqYckGEUJv689RbRiTSEHCQ7wOVeALbkOz999YpqT46yMRIGtSNl2iCL1waAZSx40+h59NV/EwzV/A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/is-stream/-/is-stream-4.0.1.tgz}
    name: is-stream
    version: 4.0.1
    engines: {node: '>=18'}
    dev: true

  registry.npmjs.org/is-unicode-supported/2.1.0:
    resolution: {integrity: sha512-mE00Gnza5EEB3Ds0HfMyllZzbBrmLOX3vfWoj9A9PEnTfratQ/BcaJOuMhnkhjXvb2+FkY3VuHqtAGpTPmglFQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-2.1.0.tgz}
    name: is-unicode-supported
    version: 2.1.0
    engines: {node: '>=18'}
    dev: true

  registry.npmjs.org/is-what/4.1.16:
    resolution: {integrity: sha512-ZhMwEosbFJkA0YhFnNDgTM4ZxDRsS6HqTo7qsZM08fehyRYIYa0yHu5R6mgo1n/8MgaPBXiPimPD77baVFYg+A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/is-what/-/is-what-4.1.16.tgz}
    name: is-what
    version: 4.1.16
    engines: {node: '>=12.13'}

  registry.npmjs.org/is-wsl/3.1.0:
    resolution: {integrity: sha512-UcVfVfaK4Sc4m7X3dUSoHoozQGBEFeDC+zVo06t98xe8CzHSZZBekNXH+tu0NalHolcJ/QAGqS46Hef7QXBIMw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/is-wsl/-/is-wsl-3.1.0.tgz}
    name: is-wsl
    version: 3.1.0
    engines: {node: '>=16'}
    dependencies:
      is-inside-container: registry.npmjs.org/is-inside-container/1.0.0
    dev: true

  registry.npmjs.org/isexe/2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz}
    name: isexe
    version: 2.0.0
    dev: true

  registry.npmjs.org/isexe/3.1.1:
    resolution: {integrity: sha512-LpB/54B+/2J5hqQ7imZHfdU31OlgQqx7ZicVlkm9kzg9/w8GKLEcFfJl/t7DCEDueOyBAD6zCCwTO6Fzs0NoEQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/isexe/-/isexe-3.1.1.tgz}
    name: isexe
    version: 3.1.1
    engines: {node: '>=16'}
    dev: true

  registry.npmjs.org/jiti/2.6.0:
    resolution: {integrity: sha512-VXe6RjJkBPj0ohtqaO8vSWP3ZhAKo66fKrFNCll4BTcwljPLz03pCbaNKfzGP5MbrCYcbJ7v0nOYYwUzTEIdXQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/jiti/-/jiti-2.6.0.tgz}
    name: jiti
    version: 2.6.0
    hasBin: true
    dev: true

  registry.npmjs.org/js-tokens/4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz}
    name: js-tokens
    version: 4.0.0
    dev: true

  registry.npmjs.org/js-yaml/4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz}
    name: js-yaml
    version: 4.1.0
    hasBin: true
    dependencies:
      argparse: registry.npmjs.org/argparse/2.0.1
    dev: true

  registry.npmjs.org/jsesc/3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz}
    name: jsesc
    version: 3.1.0
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  registry.npmjs.org/json-buffer/3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz}
    name: json-buffer
    version: 3.0.1
    dev: true

  registry.npmjs.org/json-parse-even-better-errors/4.0.0:
    resolution: {integrity: sha512-lR4MXjGNgkJc7tkQ97kb2nuEMnNCyU//XYVH0MKTGcXEiSudQ5MKGKen3C5QubYy0vmq+JGitUg92uuywGEwIA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-4.0.0.tgz}
    name: json-parse-even-better-errors
    version: 4.0.0
    engines: {node: ^18.17.0 || >=20.5.0}
    dev: true

  registry.npmjs.org/json-schema-traverse/0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz}
    name: json-schema-traverse
    version: 0.4.1
    dev: true

  registry.npmjs.org/json-stable-stringify-without-jsonify/1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz}
    name: json-stable-stringify-without-jsonify
    version: 1.0.1
    dev: true

  registry.npmjs.org/json5/2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/json5/-/json5-2.2.3.tgz}
    name: json5
    version: 2.2.3
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  registry.npmjs.org/keyv/4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz}
    name: keyv
    version: 4.5.4
    dependencies:
      json-buffer: registry.npmjs.org/json-buffer/3.0.1
    dev: true

  registry.npmjs.org/kolorist/1.8.0:
    resolution: {integrity: sha512-Y+60/zizpJ3HRH8DCss+q95yr6145JXZo46OTpFvDZWLfRCE4qChOyk1b26nMaNpfHHgxagk9dXT5OP0Tfe+dQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/kolorist/-/kolorist-1.8.0.tgz}
    name: kolorist
    version: 1.8.0
    dev: true

  registry.npmjs.org/levn/0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/levn/-/levn-0.4.1.tgz}
    name: levn
    version: 0.4.1
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: registry.npmjs.org/prelude-ls/1.2.1
      type-check: registry.npmjs.org/type-check/0.4.0
    dev: true

  registry.npmjs.org/local-pkg/1.1.2:
    resolution: {integrity: sha512-arhlxbFRmoQHl33a0Zkle/YWlmNwoyt6QNZEIJcqNbdrsix5Lvc4HyyI3EnwxTYlZYc32EbYrQ8SzEZ7dqgg9A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/local-pkg/-/local-pkg-1.1.2.tgz}
    name: local-pkg
    version: 1.1.2
    engines: {node: '>=14'}
    dependencies:
      mlly: registry.npmjs.org/mlly/1.8.0
      pkg-types: registry.npmjs.org/pkg-types/2.3.0
      quansync: registry.npmjs.org/quansync/0.2.11
    dev: true

  registry.npmjs.org/locate-path/6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz}
    name: locate-path
    version: 6.0.0
    engines: {node: '>=10'}
    dependencies:
      p-locate: registry.npmjs.org/p-locate/5.0.0
    dev: true

  registry.npmjs.org/lodash.merge/4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz}
    name: lodash.merge
    version: 4.6.2
    dev: true

  registry.npmjs.org/lru-cache/5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz}
    name: lru-cache
    version: 5.1.1
    dependencies:
      yallist: registry.npmjs.org/yallist/3.1.1
    dev: true

  registry.npmjs.org/magic-string/0.30.19:
    resolution: {integrity: sha512-2N21sPY9Ws53PZvsEpVtNuSW+ScYbQdp4b9qUaL+9QkHUrGFKo56Lg9Emg5s9V/qrtNBmiR01sYhUOwu3H+VOw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/magic-string/-/magic-string-0.30.19.tgz}
    name: magic-string
    version: 0.30.19
    dependencies:
      '@jridgewell/sourcemap-codec': registry.npmjs.org/@jridgewell/sourcemap-codec/1.5.5

  registry.npmjs.org/mdn-data/2.12.2:
    resolution: {integrity: sha512-IEn+pegP1aManZuckezWCO+XZQDplx1366JoVhTpMpBB1sPey/SbveZQUosKiKiGYjg1wH4pMlNgXbCiYgihQA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/mdn-data/-/mdn-data-2.12.2.tgz}
    name: mdn-data
    version: 2.12.2
    dev: true

  registry.npmjs.org/memorystream/0.3.1:
    resolution: {integrity: sha512-S3UwM3yj5mtUSEfP41UZmt/0SCoVYUcU1rkXv+BQ5Ig8ndL4sPoJNBUJERafdPb5jjHJGuMgytgKvKIf58XNBw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/memorystream/-/memorystream-0.3.1.tgz}
    name: memorystream
    version: 0.3.1
    engines: {node: '>= 0.10.0'}
    dev: true

  registry.npmjs.org/merge2/1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz}
    name: merge2
    version: 1.4.1
    engines: {node: '>= 8'}
    dev: true

  registry.npmjs.org/micromatch/4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz}
    name: micromatch
    version: 4.0.8
    engines: {node: '>=8.6'}
    dependencies:
      braces: registry.npmjs.org/braces/3.0.3
      picomatch: registry.npmjs.org/picomatch/2.3.1
    dev: true

  registry.npmjs.org/minimatch/3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz}
    name: minimatch
    version: 3.1.2
    dependencies:
      brace-expansion: registry.npmjs.org/brace-expansion/1.1.12
    dev: true

  registry.npmjs.org/minimatch/9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz}
    name: minimatch
    version: 9.0.5
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: registry.npmjs.org/brace-expansion/2.0.2
    dev: true

  registry.npmjs.org/mitt/3.0.1:
    resolution: {integrity: sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/mitt/-/mitt-3.0.1.tgz}
    name: mitt
    version: 3.0.1

  registry.npmjs.org/mlly/1.8.0:
    resolution: {integrity: sha512-l8D9ODSRWLe2KHJSifWGwBqpTZXIXTeo8mlKjY+E2HAakaTeNpqAyBZ8GSqLzHgw4XmHmC8whvpjJNMbFZN7/g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/mlly/-/mlly-1.8.0.tgz}
    name: mlly
    version: 1.8.0
    dependencies:
      acorn: registry.npmjs.org/acorn/8.15.0
      pathe: registry.npmjs.org/pathe/2.0.3
      pkg-types: registry.npmjs.org/pkg-types/1.3.1
      ufo: registry.npmjs.org/ufo/1.6.1
    dev: true

  registry.npmjs.org/mrmime/2.0.1:
    resolution: {integrity: sha512-Y3wQdFg2Va6etvQ5I82yUhGdsKrcYox6p7FfL1LbK2J4V01F9TGlepTIhnK24t7koZibmg82KGglhA1XK5IsLQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/mrmime/-/mrmime-2.0.1.tgz}
    name: mrmime
    version: 2.0.1
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/ms/2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/ms/-/ms-2.1.3.tgz}
    name: ms
    version: 2.1.3
    dev: true

  registry.npmjs.org/muggle-string/0.4.1:
    resolution: {integrity: sha512-VNTrAak/KhO2i8dqqnqnAHOa3cYBwXEZe9h+D5h/1ZqFSTEFHdM65lR7RoIqq3tBBYavsOXV84NoHXZ0AkPyqQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/muggle-string/-/muggle-string-0.4.1.tgz}
    name: muggle-string
    version: 0.4.1
    dev: true

  registry.npmjs.org/nanoid/3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz}
    name: nanoid
    version: 3.3.11
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  registry.npmjs.org/nanoid/5.1.6:
    resolution: {integrity: sha512-c7+7RQ+dMB5dPwwCp4ee1/iV/q2P6aK1mTZcfr1BTuVlyW9hJYiMPybJCcnBlQtuSmTIWNeazm/zqNoZSSElBg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/nanoid/-/nanoid-5.1.6.tgz}
    name: nanoid
    version: 5.1.6
    engines: {node: ^18 || >=20}
    hasBin: true
    dev: true

  registry.npmjs.org/natural-compare/1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz}
    name: natural-compare
    version: 1.4.0
    dev: true

  registry.npmjs.org/node-addon-api/7.1.1:
    resolution: {integrity: sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/node-addon-api/-/node-addon-api-7.1.1.tgz}
    name: node-addon-api
    version: 7.1.1
    dev: true
    optional: true

  registry.npmjs.org/node-fetch-native/1.6.7:
    resolution: {integrity: sha512-g9yhqoedzIUm0nTnTqAQvueMPVOuIY16bqgAJJC8XOOubYFNwz6IER9qs0Gq2Xd0+CecCKFjtdDTMA4u4xG06Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/node-fetch-native/-/node-fetch-native-1.6.7.tgz}
    name: node-fetch-native
    version: 1.6.7
    dev: true

  registry.npmjs.org/node-releases/2.0.21:
    resolution: {integrity: sha512-5b0pgg78U3hwXkCM8Z9b2FJdPZlr9Psr9V2gQPESdGHqbntyFJKFW4r5TeWGFzafGY3hzs1JC62VEQMbl1JFkw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/node-releases/-/node-releases-2.0.21.tgz}
    name: node-releases
    version: 2.0.21
    dev: true

  registry.npmjs.org/normalize-path/3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz}
    name: normalize-path
    version: 3.0.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/normalize-range/0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz}
    name: normalize-range
    version: 0.1.2
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/npm-normalize-package-bin/4.0.0:
    resolution: {integrity: sha512-TZKxPvItzai9kN9H/TkmCtx/ZN/hvr3vUycjlfmH0ootY9yFBzNOpiXAdIn1Iteqsvk4lQn6B5PTrt+n6h8k/w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-4.0.0.tgz}
    name: npm-normalize-package-bin
    version: 4.0.0
    engines: {node: ^18.17.0 || >=20.5.0}
    dev: true

  registry.npmjs.org/npm-run-all2/8.0.4:
    resolution: {integrity: sha512-wdbB5My48XKp2ZfJUlhnLVihzeuA1hgBnqB2J9ahV77wLS+/YAJAlN8I+X3DIFIPZ3m5L7nplmlbhNiFDmXRDA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/npm-run-all2/-/npm-run-all2-8.0.4.tgz}
    name: npm-run-all2
    version: 8.0.4
    engines: {node: ^20.5.0 || >=22.0.0, npm: '>= 10'}
    hasBin: true
    dependencies:
      ansi-styles: registry.npmjs.org/ansi-styles/6.2.3
      cross-spawn: registry.npmjs.org/cross-spawn/7.0.6
      memorystream: registry.npmjs.org/memorystream/0.3.1
      picomatch: registry.npmjs.org/picomatch/4.0.3
      pidtree: registry.npmjs.org/pidtree/0.6.0
      read-package-json-fast: registry.npmjs.org/read-package-json-fast/4.0.0
      shell-quote: registry.npmjs.org/shell-quote/1.8.3
      which: registry.npmjs.org/which/5.0.0
    dev: true

  registry.npmjs.org/npm-run-path/6.0.0:
    resolution: {integrity: sha512-9qny7Z9DsQU8Ou39ERsPU4OZQlSTP47ShQzuKZ6PRXpYLtIFgl/DEBYEXKlvcEa+9tHVcK8CF81Y2V72qaZhWA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/npm-run-path/-/npm-run-path-6.0.0.tgz}
    name: npm-run-path
    version: 6.0.0
    engines: {node: '>=18'}
    dependencies:
      path-key: registry.npmjs.org/path-key/4.0.0
      unicorn-magic: registry.npmjs.org/unicorn-magic/0.3.0
    dev: true

  registry.npmjs.org/nth-check/2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/nth-check/-/nth-check-2.1.1.tgz}
    name: nth-check
    version: 2.1.1
    dependencies:
      boolbase: registry.npmjs.org/boolbase/1.0.0
    dev: true

  registry.npmjs.org/ofetch/1.4.1:
    resolution: {integrity: sha512-QZj2DfGplQAr2oj9KzceK9Hwz6Whxazmn85yYeVuS3u9XTMOGMRx0kO95MQ+vLsj/S/NwBDMMLU5hpxvI6Tklw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/ofetch/-/ofetch-1.4.1.tgz}
    name: ofetch
    version: 1.4.1
    dependencies:
      destr: registry.npmjs.org/destr/2.0.5
      node-fetch-native: registry.npmjs.org/node-fetch-native/1.6.7
      ufo: registry.npmjs.org/ufo/1.6.1
    dev: true

  registry.npmjs.org/ohash/2.0.11:
    resolution: {integrity: sha512-RdR9FQrFwNBNXAr4GixM8YaRZRJ5PUWbKYbE5eOsrwAjJW0q2REGcf79oYPsLyskQCZG1PLN+S/K1V00joZAoQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/ohash/-/ohash-2.0.11.tgz}
    name: ohash
    version: 2.0.11
    dev: true

  registry.npmjs.org/open/10.2.0:
    resolution: {integrity: sha512-YgBpdJHPyQ2UE5x+hlSXcnejzAvD0b22U2OuAP+8OnlJT+PjWPxtgmGqKKc+RgTM63U9gN0YzrYc71R2WT/hTA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/open/-/open-10.2.0.tgz}
    name: open
    version: 10.2.0
    engines: {node: '>=18'}
    dependencies:
      default-browser: registry.npmjs.org/default-browser/5.2.1
      define-lazy-prop: registry.npmjs.org/define-lazy-prop/3.0.0
      is-inside-container: registry.npmjs.org/is-inside-container/1.0.0
      wsl-utils: registry.npmjs.org/wsl-utils/0.1.0
    dev: true

  registry.npmjs.org/optionator/0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz}
    name: optionator
    version: 0.9.4
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: registry.npmjs.org/deep-is/0.1.4
      fast-levenshtein: registry.npmjs.org/fast-levenshtein/2.0.6
      levn: registry.npmjs.org/levn/0.4.1
      prelude-ls: registry.npmjs.org/prelude-ls/1.2.1
      type-check: registry.npmjs.org/type-check/0.4.0
      word-wrap: registry.npmjs.org/word-wrap/1.2.5
    dev: true

  registry.npmjs.org/p-limit/3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz}
    name: p-limit
    version: 3.1.0
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: registry.npmjs.org/yocto-queue/0.1.0
    dev: true

  registry.npmjs.org/p-locate/5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz}
    name: p-locate
    version: 5.0.0
    engines: {node: '>=10'}
    dependencies:
      p-limit: registry.npmjs.org/p-limit/3.1.0
    dev: true

  registry.npmjs.org/package-manager-detector/1.3.0:
    resolution: {integrity: sha512-ZsEbbZORsyHuO00lY1kV3/t72yp6Ysay6Pd17ZAlNGuGwmWDLCJxFpRs0IzfXfj1o4icJOkUEioexFHzyPurSQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/package-manager-detector/-/package-manager-detector-1.3.0.tgz}
    name: package-manager-detector
    version: 1.3.0
    dev: true

  registry.npmjs.org/parent-module/1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz}
    name: parent-module
    version: 1.0.1
    engines: {node: '>=6'}
    dependencies:
      callsites: registry.npmjs.org/callsites/3.1.0
    dev: true

  registry.npmjs.org/parse-ms/4.0.0:
    resolution: {integrity: sha512-TXfryirbmq34y8QBwgqCVLi+8oA3oWx2eAnSn62ITyEhEYaWRlVZ2DvMM9eZbMs/RfxPu/PK/aBLyGj4IrqMHw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/parse-ms/-/parse-ms-4.0.0.tgz}
    name: parse-ms
    version: 4.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmjs.org/path-browserify/1.0.1:
    resolution: {integrity: sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/path-browserify/-/path-browserify-1.0.1.tgz}
    name: path-browserify
    version: 1.0.1
    dev: true

  registry.npmjs.org/path-exists/4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz}
    name: path-exists
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/path-key/3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz}
    name: path-key
    version: 3.1.1
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/path-key/4.0.0:
    resolution: {integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/path-key/-/path-key-4.0.0.tgz}
    name: path-key
    version: 4.0.0
    engines: {node: '>=12'}
    dev: true

  registry.npmjs.org/pathe/2.0.3:
    resolution: {integrity: sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz}
    name: pathe
    version: 2.0.3
    dev: true

  registry.npmjs.org/perfect-debounce/1.0.0:
    resolution: {integrity: sha512-xCy9V055GLEqoFaHoC1SoLIaLmWctgCUaBaWxDZ7/Zx4CTyX7cJQLJOok/orfjZAh9kEYpjJa4d0KcJmCbctZA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/perfect-debounce/-/perfect-debounce-1.0.0.tgz}
    name: perfect-debounce
    version: 1.0.0

  registry.npmjs.org/perfect-debounce/2.0.0:
    resolution: {integrity: sha512-fkEH/OBiKrqqI/yIgjR92lMfs2K8105zt/VT6+7eTjNwisrsh47CeIED9z58zI7DfKdH3uHAn25ziRZn3kgAow==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/perfect-debounce/-/perfect-debounce-2.0.0.tgz}
    name: perfect-debounce
    version: 2.0.0
    dev: true

  registry.npmjs.org/picocolors/1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz}
    name: picocolors
    version: 1.1.1

  registry.npmjs.org/picomatch/2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz}
    name: picomatch
    version: 2.3.1
    engines: {node: '>=8.6'}
    dev: true

  registry.npmjs.org/picomatch/4.0.3:
    resolution: {integrity: sha512-5gTmgEY/sqK6gFXLIsQNH19lWb4ebPDLA4SdLP7dsWkIXHWlG66oPuVvXSGFPppYZz8ZDZq0dYYrbHfBCVUb1Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/picomatch/-/picomatch-4.0.3.tgz}
    name: picomatch
    version: 4.0.3
    engines: {node: '>=12'}
    dev: true

  registry.npmjs.org/pidtree/0.6.0:
    resolution: {integrity: sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/pidtree/-/pidtree-0.6.0.tgz}
    name: pidtree
    version: 0.6.0
    engines: {node: '>=0.10'}
    hasBin: true
    dev: true

  registry.npmjs.org/pinia/3.0.3_5w2wwtazrjreqxulnlwvyyqa5u:
    resolution: {integrity: sha512-ttXO/InUULUXkMHpTdp9Fj4hLpD/2AoJdmAbAeW2yu1iy1k+pkFekQXw5VpC0/5p51IOR/jDaDRfRWRnMMsGOA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/pinia/-/pinia-3.0.3.tgz}
    id: registry.npmjs.org/pinia/3.0.3
    name: pinia
    version: 3.0.3
    peerDependencies:
      typescript: '>=4.4.4'
      vue: ^2.7.0 || ^3.5.11
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@vue/devtools-api': registry.npmjs.org/@vue/devtools-api/7.7.7
      typescript: registry.npmjs.org/typescript/5.8.3
      vue: registry.npmjs.org/vue/3.5.22_typescript@5.8.3
    dev: false

  registry.npmjs.org/pkg-types/1.3.1:
    resolution: {integrity: sha512-/Jm5M4RvtBFVkKWRu2BLUTNP8/M2a+UwuAX+ae4770q1qVGtfjG+WTCupoZixokjmHiry8uI+dlY8KXYV5HVVQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/pkg-types/-/pkg-types-1.3.1.tgz}
    name: pkg-types
    version: 1.3.1
    dependencies:
      confbox: registry.npmjs.org/confbox/0.1.8
      mlly: registry.npmjs.org/mlly/1.8.0
      pathe: registry.npmjs.org/pathe/2.0.3
    dev: true

  registry.npmjs.org/pkg-types/2.3.0:
    resolution: {integrity: sha512-SIqCzDRg0s9npO5XQ3tNZioRY1uK06lA41ynBC1YmFTmnY6FjUjVt6s4LoADmwoig1qqD0oK8h1p/8mlMx8Oig==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/pkg-types/-/pkg-types-2.3.0.tgz}
    name: pkg-types
    version: 2.3.0
    dependencies:
      confbox: registry.npmjs.org/confbox/0.2.2
      exsolve: registry.npmjs.org/exsolve/1.0.7
      pathe: registry.npmjs.org/pathe/2.0.3
    dev: true

  registry.npmjs.org/postcss-selector-parser/6.0.10:
    resolution: {integrity: sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.10.tgz}
    name: postcss-selector-parser
    version: 6.0.10
    engines: {node: '>=4'}
    dependencies:
      cssesc: registry.npmjs.org/cssesc/3.0.0
      util-deprecate: registry.npmjs.org/util-deprecate/1.0.2
    dev: true

  registry.npmjs.org/postcss-selector-parser/6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz}
    name: postcss-selector-parser
    version: 6.1.2
    engines: {node: '>=4'}
    dependencies:
      cssesc: registry.npmjs.org/cssesc/3.0.0
      util-deprecate: registry.npmjs.org/util-deprecate/1.0.2
    dev: true

  registry.npmjs.org/postcss-value-parser/4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz}
    name: postcss-value-parser
    version: 4.2.0
    dev: true

  registry.npmjs.org/postcss/8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz}
    name: postcss
    version: 8.5.6
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: registry.npmjs.org/nanoid/3.3.11
      picocolors: registry.npmjs.org/picocolors/1.1.1
      source-map-js: registry.npmjs.org/source-map-js/1.2.1

  registry.npmjs.org/prelude-ls/1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz}
    name: prelude-ls
    version: 1.2.1
    engines: {node: '>= 0.8.0'}
    dev: true

  registry.npmjs.org/prettier-linter-helpers/1.0.0:
    resolution: {integrity: sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz}
    name: prettier-linter-helpers
    version: 1.0.0
    engines: {node: '>=6.0.0'}
    dependencies:
      fast-diff: registry.npmjs.org/fast-diff/1.3.0
    dev: true

  registry.npmjs.org/prettier/3.6.2:
    resolution: {integrity: sha512-I7AIg5boAr5R0FFtJ6rCfD+LFsWHp81dolrFD8S79U9tb8Az2nGrJncnMSnys+bpQJfRUzqs9hnA81OAA3hCuQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/prettier/-/prettier-3.6.2.tgz}
    name: prettier
    version: 3.6.2
    engines: {node: '>=14'}
    hasBin: true
    dev: true

  registry.npmjs.org/pretty-ms/9.3.0:
    resolution: {integrity: sha512-gjVS5hOP+M3wMm5nmNOucbIrqudzs9v/57bWRHQWLYklXqoXKrVfYW2W9+glfGsqtPgpiz5WwyEEB+ksXIx3gQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/pretty-ms/-/pretty-ms-9.3.0.tgz}
    name: pretty-ms
    version: 9.3.0
    engines: {node: '>=18'}
    dependencies:
      parse-ms: registry.npmjs.org/parse-ms/4.0.0
    dev: true

  registry.npmjs.org/punycode/2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz}
    name: punycode
    version: 2.3.1
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/quansync/0.2.11:
    resolution: {integrity: sha512-AifT7QEbW9Nri4tAwR5M/uzpBuqfZf+zwaEM/QkzEjj7NBuFD2rBuy0K3dE+8wltbezDV7JMA0WfnCPYRSYbXA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/quansync/-/quansync-0.2.11.tgz}
    name: quansync
    version: 0.2.11
    dev: true

  registry.npmjs.org/queue-microtask/1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz}
    name: queue-microtask
    version: 1.2.3
    dev: true

  registry.npmjs.org/read-package-json-fast/4.0.0:
    resolution: {integrity: sha512-qpt8EwugBWDw2cgE2W+/3oxC+KTez2uSVR8JU9Q36TXPAGCaozfQUs59v4j4GFpWTaw0i6hAZSvOmu1J0uOEUg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/read-package-json-fast/-/read-package-json-fast-4.0.0.tgz}
    name: read-package-json-fast
    version: 4.0.0
    engines: {node: ^18.17.0 || >=20.5.0}
    dependencies:
      json-parse-even-better-errors: registry.npmjs.org/json-parse-even-better-errors/4.0.0
      npm-normalize-package-bin: registry.npmjs.org/npm-normalize-package-bin/4.0.0
    dev: true

  registry.npmjs.org/readdirp/3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz}
    name: readdirp
    version: 3.6.0
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: registry.npmjs.org/picomatch/2.3.1
    dev: true

  registry.npmjs.org/readdirp/4.1.2:
    resolution: {integrity: sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/readdirp/-/readdirp-4.1.2.tgz}
    name: readdirp
    version: 4.1.2
    engines: {node: '>= 14.18.0'}
    dev: true

  registry.npmjs.org/remarkable/2.0.1:
    resolution: {integrity: sha512-YJyMcOH5lrR+kZdmB0aJJ4+93bEojRZ1HGDn9Eagu6ibg7aVZhc3OWbbShRid+Q5eAfsEqWxpe+g5W5nYNfNiA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/remarkable/-/remarkable-2.0.1.tgz}
    name: remarkable
    version: 2.0.1
    engines: {node: '>= 6.0.0'}
    hasBin: true
    dependencies:
      argparse: registry.npmjs.org/argparse/1.0.10
      autolinker: registry.npmjs.org/autolinker/3.16.2
    dev: false

  registry.npmjs.org/resolve-from/4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz}
    name: resolve-from
    version: 4.0.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/reusify/1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz}
    name: reusify
    version: 1.1.0
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/rfdc/1.4.1:
    resolution: {integrity: sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/rfdc/-/rfdc-1.4.1.tgz}
    name: rfdc
    version: 1.4.1

  registry.npmjs.org/rollup/4.52.2:
    resolution: {integrity: sha512-I25/2QgoROE1vYV+NQ1En9T9UFB9Cmfm2CJ83zZOlaDpvz29wGQSZXWKw7MiNXau7wYgB/T9fVIdIuEQ+KbiiA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/rollup/-/rollup-4.52.2.tgz}
    name: rollup
    version: 4.52.2
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true
    dependencies:
      '@types/estree': registry.npmjs.org/@types/estree/1.0.8
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': registry.npmjs.org/@rollup/rollup-android-arm-eabi/4.52.2
      '@rollup/rollup-android-arm64': registry.npmjs.org/@rollup/rollup-android-arm64/4.52.2
      '@rollup/rollup-darwin-arm64': registry.npmjs.org/@rollup/rollup-darwin-arm64/4.52.2
      '@rollup/rollup-darwin-x64': registry.npmjs.org/@rollup/rollup-darwin-x64/4.52.2
      '@rollup/rollup-freebsd-arm64': registry.npmjs.org/@rollup/rollup-freebsd-arm64/4.52.2
      '@rollup/rollup-freebsd-x64': registry.npmjs.org/@rollup/rollup-freebsd-x64/4.52.2
      '@rollup/rollup-linux-arm-gnueabihf': registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/4.52.2
      '@rollup/rollup-linux-arm-musleabihf': registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/4.52.2
      '@rollup/rollup-linux-arm64-gnu': registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/4.52.2
      '@rollup/rollup-linux-arm64-musl': registry.npmjs.org/@rollup/rollup-linux-arm64-musl/4.52.2
      '@rollup/rollup-linux-loong64-gnu': registry.npmjs.org/@rollup/rollup-linux-loong64-gnu/4.52.2
      '@rollup/rollup-linux-ppc64-gnu': registry.npmjs.org/@rollup/rollup-linux-ppc64-gnu/4.52.2
      '@rollup/rollup-linux-riscv64-gnu': registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/4.52.2
      '@rollup/rollup-linux-riscv64-musl': registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/4.52.2
      '@rollup/rollup-linux-s390x-gnu': registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/4.52.2
      '@rollup/rollup-linux-x64-gnu': registry.npmjs.org/@rollup/rollup-linux-x64-gnu/4.52.2
      '@rollup/rollup-linux-x64-musl': registry.npmjs.org/@rollup/rollup-linux-x64-musl/4.52.2
      '@rollup/rollup-openharmony-arm64': registry.npmjs.org/@rollup/rollup-openharmony-arm64/4.52.2
      '@rollup/rollup-win32-arm64-msvc': registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/4.52.2
      '@rollup/rollup-win32-ia32-msvc': registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/4.52.2
      '@rollup/rollup-win32-x64-gnu': registry.npmjs.org/@rollup/rollup-win32-x64-gnu/4.52.2
      '@rollup/rollup-win32-x64-msvc': registry.npmjs.org/@rollup/rollup-win32-x64-msvc/4.52.2
      fsevents: registry.npmjs.org/fsevents/2.3.3
    dev: true

  registry.npmjs.org/run-applescript/7.1.0:
    resolution: {integrity: sha512-DPe5pVFaAsinSaV6QjQ6gdiedWDcRCbUuiQfQa2wmWV7+xC9bGulGI8+TdRmoFkAPaBXk8CrAbnlY2ISniJ47Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/run-applescript/-/run-applescript-7.1.0.tgz}
    name: run-applescript
    version: 7.1.0
    engines: {node: '>=18'}
    dev: true

  registry.npmjs.org/run-parallel/1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz}
    name: run-parallel
    version: 1.2.0
    dependencies:
      queue-microtask: registry.npmjs.org/queue-microtask/1.2.3
    dev: true

  registry.npmjs.org/sass/1.93.2:
    resolution: {integrity: sha512-t+YPtOQHpGW1QWsh1CHQ5cPIr9lbbGZLZnbihP/D/qZj/yuV68m8qarcV17nvkOX81BCrvzAlq2klCQFZghyTg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/sass/-/sass-1.93.2.tgz}
    name: sass
    version: 1.93.2
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      chokidar: registry.npmjs.org/chokidar/4.0.3
      immutable: registry.npmjs.org/immutable/5.1.3
      source-map-js: registry.npmjs.org/source-map-js/1.2.1
    optionalDependencies:
      '@parcel/watcher': registry.npmjs.org/@parcel/watcher/2.5.1
    dev: true

  registry.npmjs.org/semver/6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/semver/-/semver-6.3.1.tgz}
    name: semver
    version: 6.3.1
    hasBin: true
    dev: true

  registry.npmjs.org/semver/7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/semver/-/semver-7.7.2.tgz}
    name: semver
    version: 7.7.2
    engines: {node: '>=10'}
    hasBin: true
    dev: true

  registry.npmjs.org/shebang-command/2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz}
    name: shebang-command
    version: 2.0.0
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: registry.npmjs.org/shebang-regex/3.0.0
    dev: true

  registry.npmjs.org/shebang-regex/3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz}
    name: shebang-regex
    version: 3.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/shell-quote/1.8.3:
    resolution: {integrity: sha512-ObmnIF4hXNg1BqhnHmgbDETF8dLPCggZWBjkQfhZpbszZnYur5DUljTcCHii5LC3J5E0yeO/1LIMyH+UvHQgyw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.3.tgz}
    name: shell-quote
    version: 1.8.3
    engines: {node: '>= 0.4'}
    dev: true

  registry.npmjs.org/signal-exit/4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz}
    name: signal-exit
    version: 4.1.0
    engines: {node: '>=14'}
    dev: true

  registry.npmjs.org/sirv/3.0.2:
    resolution: {integrity: sha512-2wcC/oGxHis/BoHkkPwldgiPSYcpZK3JU28WoMVv55yHJgcZ8rlXvuG9iZggz+sU1d4bRgIGASwyWqjxu3FM0g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/sirv/-/sirv-3.0.2.tgz}
    name: sirv
    version: 3.0.2
    engines: {node: '>=18'}
    dependencies:
      '@polka/url': registry.npmjs.org/@polka/url/1.0.0-next.29
      mrmime: registry.npmjs.org/mrmime/2.0.1
      totalist: registry.npmjs.org/totalist/3.0.1
    dev: true

  registry.npmjs.org/source-map-js/1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz}
    name: source-map-js
    version: 1.2.1
    engines: {node: '>=0.10.0'}

  registry.npmjs.org/speakingurl/14.0.1:
    resolution: {integrity: sha512-1POYv7uv2gXoyGFpBCmpDVSNV74IfsWlDW216UPjbWufNf+bSU6GdbDsxdcxtfwb4xlI3yxzOTKClUosxARYrQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/speakingurl/-/speakingurl-14.0.1.tgz}
    name: speakingurl
    version: 14.0.1
    engines: {node: '>=0.10.0'}

  registry.npmjs.org/speech-to-element/1.0.4:
    resolution: {integrity: sha512-0wDab5u0vbch7taU48N4ccqNBqHMX7EtrOeZdfwfrZ3e4Cw4OTyZZziLjCouTjWaT/oe4SmpK26/28SjNLxFLQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/speech-to-element/-/speech-to-element-1.0.4.tgz}
    name: speech-to-element
    version: 1.0.4
    dev: false

  registry.npmjs.org/sprintf-js/1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz}
    name: sprintf-js
    version: 1.0.3
    dev: false

  registry.npmjs.org/strip-final-newline/4.0.0:
    resolution: {integrity: sha512-aulFJcD6YK8V1G7iRB5tigAP4TsHBZZrOV8pjV++zdUwmeV8uzbY7yn6h9MswN62adStNZFuCIx4haBnRuMDaw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-4.0.0.tgz}
    name: strip-final-newline
    version: 4.0.0
    engines: {node: '>=18'}
    dev: true

  registry.npmjs.org/strip-json-comments/3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz}
    name: strip-json-comments
    version: 3.1.1
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/superjson/2.2.2:
    resolution: {integrity: sha512-5JRxVqC8I8NuOUjzBbvVJAKNM8qoVuH0O77h4WInc/qC2q5IreqKxYwgkga3PfA22OayK2ikceb/B26dztPl+Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/superjson/-/superjson-2.2.2.tgz}
    name: superjson
    version: 2.2.2
    engines: {node: '>=16'}
    dependencies:
      copy-anything: registry.npmjs.org/copy-anything/3.0.5

  registry.npmjs.org/supports-color/7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz}
    name: supports-color
    version: 7.2.0
    engines: {node: '>=8'}
    dependencies:
      has-flag: registry.npmjs.org/has-flag/4.0.0
    dev: true

  registry.npmjs.org/synckit/0.11.11:
    resolution: {integrity: sha512-MeQTA1r0litLUf0Rp/iisCaL8761lKAZHaimlbGK4j0HysC4PLfqygQj9srcs0m2RdtDYnF8UuYyKpbjHYp7Jw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/synckit/-/synckit-0.11.11.tgz}
    name: synckit
    version: 0.11.11
    engines: {node: ^14.18.0 || >=16.0.0}
    dependencies:
      '@pkgr/core': registry.npmjs.org/@pkgr/core/0.2.9
    dev: true

  registry.npmjs.org/tinyexec/1.0.1:
    resolution: {integrity: sha512-5uC6DDlmeqiOwCPmK9jMSdOuZTh8bU39Ys6yidB+UTt5hfZUPGAypSgFRiEp+jbi9qH40BLDvy85jIU88wKSqw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/tinyexec/-/tinyexec-1.0.1.tgz}
    name: tinyexec
    version: 1.0.1
    dev: true

  registry.npmjs.org/tinyglobby/0.2.15:
    resolution: {integrity: sha512-j2Zq4NyQYG5XMST4cbs02Ak8iJUdxRM0XI5QyxXuZOzKOINmWurp3smXu3y5wDcJrptwpSjgXHzIQxR0omXljQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.15.tgz}
    name: tinyglobby
    version: 0.2.15
    engines: {node: '>=12.0.0'}
    dependencies:
      fdir: registry.npmjs.org/fdir/6.5.0_picomatch@4.0.3
      picomatch: registry.npmjs.org/picomatch/4.0.3
    dev: true

  registry.npmjs.org/to-regex-range/5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz}
    name: to-regex-range
    version: 5.0.1
    engines: {node: '>=8.0'}
    dependencies:
      is-number: registry.npmjs.org/is-number/7.0.0
    dev: true

  registry.npmjs.org/totalist/3.0.1:
    resolution: {integrity: sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/totalist/-/totalist-3.0.1.tgz}
    name: totalist
    version: 3.0.1
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/ts-api-utils/2.1.0_typescript@5.8.3:
    resolution: {integrity: sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-2.1.0.tgz}
    id: registry.npmjs.org/ts-api-utils/2.1.0
    name: ts-api-utils
    version: 2.1.0
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'
    dependencies:
      typescript: registry.npmjs.org/typescript/5.8.3
    dev: true

  registry.npmjs.org/tslib/2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz}
    name: tslib
    version: 2.8.1
    dev: false

  registry.npmjs.org/type-check/0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz}
    name: type-check
    version: 0.4.0
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: registry.npmjs.org/prelude-ls/1.2.1
    dev: true

  registry.npmjs.org/typescript-eslint/8.44.1_mgbfndoj365cbyr3vqehozbar4:
    resolution: {integrity: sha512-0ws8uWGrUVTjEeN2OM4K1pLKHK/4NiNP/vz6ns+LjT/6sqpaYzIVFajZb1fj/IDwpsrrHb3Jy0Qm5u9CPcKaeg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/typescript-eslint/-/typescript-eslint-8.44.1.tgz}
    id: registry.npmjs.org/typescript-eslint/8.44.1
    name: typescript-eslint
    version: 8.44.1
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'
    dependencies:
      '@typescript-eslint/eslint-plugin': registry.npmjs.org/@typescript-eslint/eslint-plugin/8.44.1_zl5kzazaahwldyhrwpyoeq2o44
      '@typescript-eslint/parser': registry.npmjs.org/@typescript-eslint/parser/8.44.1_mgbfndoj365cbyr3vqehozbar4
      '@typescript-eslint/typescript-estree': registry.npmjs.org/@typescript-eslint/typescript-estree/8.44.1_typescript@5.8.3
      '@typescript-eslint/utils': registry.npmjs.org/@typescript-eslint/utils/8.44.1_mgbfndoj365cbyr3vqehozbar4
      eslint: registry.npmjs.org/eslint/9.36.0_jiti@2.6.0
      typescript: registry.npmjs.org/typescript/5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/typescript/5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz}
    name: typescript
    version: 5.8.3
    engines: {node: '>=14.17'}
    hasBin: true

  registry.npmjs.org/ufo/1.6.1:
    resolution: {integrity: sha512-9a4/uxlTWJ4+a5i0ooc1rU7C7YOw3wT+UGqdeNNHWnOF9qcMBgLRS+4IYUqbczewFx4mLEig6gawh7X6mFlEkA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/ufo/-/ufo-1.6.1.tgz}
    name: ufo
    version: 1.6.1
    dev: true

  registry.npmjs.org/unconfig/7.3.3:
    resolution: {integrity: sha512-QCkQoOnJF8L107gxfHL0uavn7WD9b3dpBcFX6HtfQYmjw2YzWxGuFQ0N0J6tE9oguCBJn9KOvfqYDCMPHIZrBA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/unconfig/-/unconfig-7.3.3.tgz}
    name: unconfig
    version: 7.3.3
    dependencies:
      '@quansync/fs': registry.npmjs.org/@quansync/fs/0.1.5
      defu: registry.npmjs.org/defu/6.1.4
      jiti: registry.npmjs.org/jiti/2.6.0
      quansync: registry.npmjs.org/quansync/0.2.11
    dev: true

  registry.npmjs.org/undici-types/6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz}
    name: undici-types
    version: 6.21.0
    dev: true

  registry.npmjs.org/unicorn-magic/0.3.0:
    resolution: {integrity: sha512-+QBBXBCvifc56fsbuxZQ6Sic3wqqc3WWaqxs58gvJrcOuN83HGTCwz3oS5phzU9LthRNE9VrJCFCLUgHeeFnfA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/unicorn-magic/-/unicorn-magic-0.3.0.tgz}
    name: unicorn-magic
    version: 0.3.0
    engines: {node: '>=18'}
    dev: true

  registry.npmjs.org/unocss/66.5.2_postcss@8.5.6+vite@6.3.6:
    resolution: {integrity: sha512-GCFq6ilalDE33dbjZKgeBHgKGLL/t87XM5sS0aqhD0RgzHhbT2f6Jtsmmg2Q3GASlk5uvJLxh9ifUvPQ13BdyQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/unocss/-/unocss-66.5.2.tgz}
    id: registry.npmjs.org/unocss/66.5.2
    name: unocss
    version: 66.5.2
    engines: {node: '>=14'}
    peerDependencies:
      '@unocss/webpack': 66.5.2
      vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0 || ^7.0.0-0
    peerDependenciesMeta:
      '@unocss/webpack':
        optional: true
      vite:
        optional: true
    dependencies:
      '@unocss/astro': registry.npmjs.org/@unocss/astro/66.5.2_vite@6.3.6
      '@unocss/cli': registry.npmjs.org/@unocss/cli/66.5.2
      '@unocss/core': registry.npmjs.org/@unocss/core/66.5.2
      '@unocss/postcss': registry.npmjs.org/@unocss/postcss/66.5.2_postcss@8.5.6
      '@unocss/preset-attributify': registry.npmjs.org/@unocss/preset-attributify/66.5.2
      '@unocss/preset-icons': registry.npmjs.org/@unocss/preset-icons/66.5.2
      '@unocss/preset-mini': registry.npmjs.org/@unocss/preset-mini/66.5.2
      '@unocss/preset-tagify': registry.npmjs.org/@unocss/preset-tagify/66.5.2
      '@unocss/preset-typography': registry.npmjs.org/@unocss/preset-typography/66.5.2
      '@unocss/preset-uno': registry.npmjs.org/@unocss/preset-uno/66.5.2
      '@unocss/preset-web-fonts': registry.npmjs.org/@unocss/preset-web-fonts/66.5.2
      '@unocss/preset-wind': registry.npmjs.org/@unocss/preset-wind/66.5.2
      '@unocss/preset-wind3': registry.npmjs.org/@unocss/preset-wind3/66.5.2
      '@unocss/preset-wind4': registry.npmjs.org/@unocss/preset-wind4/66.5.2
      '@unocss/transformer-attributify-jsx': registry.npmjs.org/@unocss/transformer-attributify-jsx/66.5.2
      '@unocss/transformer-compile-class': registry.npmjs.org/@unocss/transformer-compile-class/66.5.2
      '@unocss/transformer-directives': registry.npmjs.org/@unocss/transformer-directives/66.5.2
      '@unocss/transformer-variant-group': registry.npmjs.org/@unocss/transformer-variant-group/66.5.2
      '@unocss/vite': registry.npmjs.org/@unocss/vite/66.5.2_vite@6.3.6
      vite: registry.npmjs.org/vite/6.3.6_nnwfsygotcqvs65kz7hc73f7s4
    transitivePeerDependencies:
      - postcss
      - supports-color
    dev: true

  registry.npmjs.org/unplugin-utils/0.3.0:
    resolution: {integrity: sha512-JLoggz+PvLVMJo+jZt97hdIIIZ2yTzGgft9e9q8iMrC4ewufl62ekeW7mixBghonn2gVb/ICjyvlmOCUBnJLQg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/unplugin-utils/-/unplugin-utils-0.3.0.tgz}
    name: unplugin-utils
    version: 0.3.0
    engines: {node: '>=20.19.0'}
    dependencies:
      pathe: registry.npmjs.org/pathe/2.0.3
      picomatch: registry.npmjs.org/picomatch/4.0.3
    dev: true

  registry.npmjs.org/update-browserslist-db/1.1.3_browserslist@4.26.2:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz}
    id: registry.npmjs.org/update-browserslist-db/1.1.3
    name: update-browserslist-db
    version: 1.1.3
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: registry.npmjs.org/browserslist/4.26.2
      escalade: registry.npmjs.org/escalade/3.2.0
      picocolors: registry.npmjs.org/picocolors/1.1.1
    dev: true

  registry.npmjs.org/uri-js/4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz}
    name: uri-js
    version: 4.4.1
    dependencies:
      punycode: registry.npmjs.org/punycode/2.3.1
    dev: true

  registry.npmjs.org/util-deprecate/1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz}
    name: util-deprecate
    version: 1.0.2
    dev: true

  registry.npmjs.org/vite-dev-rpc/1.1.0_vite@6.3.6:
    resolution: {integrity: sha512-pKXZlgoXGoE8sEKiKJSng4hI1sQ4wi5YT24FCrwrLt6opmkjlqPPVmiPWWJn8M8byMxRGzp1CrFuqQs4M/Z39A==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/vite-dev-rpc/-/vite-dev-rpc-1.1.0.tgz}
    id: registry.npmjs.org/vite-dev-rpc/1.1.0
    name: vite-dev-rpc
    version: 1.1.0
    peerDependencies:
      vite: ^2.9.0 || ^3.0.0-0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.1 || ^7.0.0-0
    dependencies:
      birpc: registry.npmjs.org/birpc/2.6.1
      vite: registry.npmjs.org/vite/6.3.6_nnwfsygotcqvs65kz7hc73f7s4
      vite-hot-client: registry.npmjs.org/vite-hot-client/2.1.0_vite@6.3.6
    dev: true

  registry.npmjs.org/vite-hot-client/2.1.0_vite@6.3.6:
    resolution: {integrity: sha512-7SpgZmU7R+dDnSmvXE1mfDtnHLHQSisdySVR7lO8ceAXvM0otZeuQQ6C8LrS5d/aYyP/QZ0hI0L+dIPrm4YlFQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/vite-hot-client/-/vite-hot-client-2.1.0.tgz}
    id: registry.npmjs.org/vite-hot-client/2.1.0
    name: vite-hot-client
    version: 2.1.0
    peerDependencies:
      vite: ^2.6.0 || ^3.0.0 || ^4.0.0 || ^5.0.0-0 || ^6.0.0-0 || ^7.0.0-0
    dependencies:
      vite: registry.npmjs.org/vite/6.3.6_nnwfsygotcqvs65kz7hc73f7s4
    dev: true

  registry.npmjs.org/vite-plugin-inspect/11.3.3_vite@6.3.6:
    resolution: {integrity: sha512-u2eV5La99oHoYPHE6UvbwgEqKKOQGz86wMg40CCosP6q8BkB6e5xPneZfYagK4ojPJSj5anHCrnvC20DpwVdRA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/vite-plugin-inspect/-/vite-plugin-inspect-11.3.3.tgz}
    id: registry.npmjs.org/vite-plugin-inspect/11.3.3
    name: vite-plugin-inspect
    version: 11.3.3
    engines: {node: '>=14'}
    peerDependencies:
      '@nuxt/kit': '*'
      vite: ^6.0.0 || ^7.0.0-0
    peerDependenciesMeta:
      '@nuxt/kit':
        optional: true
    dependencies:
      ansis: registry.npmjs.org/ansis/4.1.0
      debug: registry.npmjs.org/debug/4.4.3
      error-stack-parser-es: registry.npmjs.org/error-stack-parser-es/1.0.5
      ohash: registry.npmjs.org/ohash/2.0.11
      open: registry.npmjs.org/open/10.2.0
      perfect-debounce: registry.npmjs.org/perfect-debounce/2.0.0
      sirv: registry.npmjs.org/sirv/3.0.2
      unplugin-utils: registry.npmjs.org/unplugin-utils/0.3.0
      vite: registry.npmjs.org/vite/6.3.6_nnwfsygotcqvs65kz7hc73f7s4
      vite-dev-rpc: registry.npmjs.org/vite-dev-rpc/1.1.0_vite@6.3.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/vite-plugin-vue-devtools/8.0.2_vite@6.3.6+vue@3.5.22:
    resolution: {integrity: sha512-1069qvMBcyAu3yXQlvYrkwoyLOk0lSSR/gTKy/vy+Det7TXnouGei6ZcKwr5TIe938v/14oLlp0ow6FSJkkORA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/vite-plugin-vue-devtools/-/vite-plugin-vue-devtools-8.0.2.tgz}
    id: registry.npmjs.org/vite-plugin-vue-devtools/8.0.2
    name: vite-plugin-vue-devtools
    version: 8.0.2
    engines: {node: '>=v14.21.3'}
    peerDependencies:
      vite: ^6.0.0 || ^7.0.0-0
    dependencies:
      '@vue/devtools-core': registry.npmjs.org/@vue/devtools-core/8.0.2_vite@6.3.6+vue@3.5.22
      '@vue/devtools-kit': registry.npmjs.org/@vue/devtools-kit/8.0.2
      '@vue/devtools-shared': registry.npmjs.org/@vue/devtools-shared/8.0.2
      execa: registry.npmjs.org/execa/9.6.0
      sirv: registry.npmjs.org/sirv/3.0.2
      vite: registry.npmjs.org/vite/6.3.6_nnwfsygotcqvs65kz7hc73f7s4
      vite-plugin-inspect: registry.npmjs.org/vite-plugin-inspect/11.3.3_vite@6.3.6
      vite-plugin-vue-inspector: registry.npmjs.org/vite-plugin-vue-inspector/5.3.2_vite@6.3.6
    transitivePeerDependencies:
      - '@nuxt/kit'
      - supports-color
      - vue
    dev: true

  registry.npmjs.org/vite-plugin-vue-inspector/5.3.2_vite@6.3.6:
    resolution: {integrity: sha512-YvEKooQcSiBTAs0DoYLfefNja9bLgkFM7NI2b07bE2SruuvX0MEa9cMaxjKVMkeCp5Nz9FRIdcN1rOdFVBeL6Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/vite-plugin-vue-inspector/-/vite-plugin-vue-inspector-5.3.2.tgz}
    id: registry.npmjs.org/vite-plugin-vue-inspector/5.3.2
    name: vite-plugin-vue-inspector
    version: 5.3.2
    peerDependencies:
      vite: ^3.0.0-0 || ^4.0.0-0 || ^5.0.0-0 || ^6.0.0-0 || ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core/7.28.4
      '@babel/plugin-proposal-decorators': registry.npmjs.org/@babel/plugin-proposal-decorators/7.28.0_@babel+core@7.28.4
      '@babel/plugin-syntax-import-attributes': registry.npmjs.org/@babel/plugin-syntax-import-attributes/7.27.1_@babel+core@7.28.4
      '@babel/plugin-syntax-import-meta': registry.npmjs.org/@babel/plugin-syntax-import-meta/7.10.4_@babel+core@7.28.4
      '@babel/plugin-transform-typescript': registry.npmjs.org/@babel/plugin-transform-typescript/7.28.0_@babel+core@7.28.4
      '@vue/babel-plugin-jsx': registry.npmjs.org/@vue/babel-plugin-jsx/1.5.0_@babel+core@7.28.4
      '@vue/compiler-dom': registry.npmjs.org/@vue/compiler-dom/3.5.22
      kolorist: registry.npmjs.org/kolorist/1.8.0
      magic-string: registry.npmjs.org/magic-string/0.30.19
      vite: registry.npmjs.org/vite/6.3.6_nnwfsygotcqvs65kz7hc73f7s4
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/vite/6.3.6_nnwfsygotcqvs65kz7hc73f7s4:
    resolution: {integrity: sha512-0msEVHJEScQbhkbVTb/4iHZdJ6SXp/AvxL2sjwYQFfBqleHtnCqv1J3sa9zbWz/6kW1m9Tfzn92vW+kZ1WV6QA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/vite/-/vite-6.3.6.tgz}
    id: registry.npmjs.org/vite/6.3.6
    name: vite
    version: 6.3.6
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: '>=1.21.0'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true
    dependencies:
      '@types/node': registry.npmjs.org/@types/node/22.18.6
      esbuild: registry.npmjs.org/esbuild/0.25.10
      fdir: registry.npmjs.org/fdir/6.5.0_picomatch@4.0.3
      jiti: registry.npmjs.org/jiti/2.6.0
      picomatch: registry.npmjs.org/picomatch/4.0.3
      postcss: registry.npmjs.org/postcss/8.5.6
      rollup: registry.npmjs.org/rollup/4.52.2
      sass: registry.npmjs.org/sass/1.93.2
      tinyglobby: registry.npmjs.org/tinyglobby/0.2.15
    optionalDependencies:
      fsevents: registry.npmjs.org/fsevents/2.3.3
    dev: true

  registry.npmjs.org/vscode-uri/3.1.0:
    resolution: {integrity: sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/vscode-uri/-/vscode-uri-3.1.0.tgz}
    name: vscode-uri
    version: 3.1.0
    dev: true

  registry.npmjs.org/vue-eslint-parser/10.2.0_eslint@9.36.0:
    resolution: {integrity: sha512-CydUvFOQKD928UzZhTp4pr2vWz1L+H99t7Pkln2QSPdvmURT0MoC4wUccfCnuEaihNsu9aYYyk+bep8rlfkUXw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/vue-eslint-parser/-/vue-eslint-parser-10.2.0.tgz}
    id: registry.npmjs.org/vue-eslint-parser/10.2.0
    name: vue-eslint-parser
    version: 10.2.0
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
    dependencies:
      debug: registry.npmjs.org/debug/4.4.3
      eslint: registry.npmjs.org/eslint/9.36.0_jiti@2.6.0
      eslint-scope: registry.npmjs.org/eslint-scope/8.4.0
      eslint-visitor-keys: registry.npmjs.org/eslint-visitor-keys/4.2.1
      espree: registry.npmjs.org/espree/10.4.0
      esquery: registry.npmjs.org/esquery/1.6.0
      semver: registry.npmjs.org/semver/7.7.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/vue-flow-layout/0.2.0:
    resolution: {integrity: sha512-zKgsWWkXq0xrus7H4Mc+uFs1ESrmdTXlO0YNbR6wMdPaFvosL3fMB8N7uTV308UhGy9UvTrGhIY7mVz9eN+L0Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/vue-flow-layout/-/vue-flow-layout-0.2.0.tgz}
    name: vue-flow-layout
    version: 0.2.0
    dev: true

  registry.npmjs.org/vue-router/4.5.1_vue@3.5.22:
    resolution: {integrity: sha512-ogAF3P97NPm8fJsE4by9dwSYtDwXIY1nFY9T6DyQnGHd1E2Da94w9JIolpe42LJGIl0DwOHBi8TcRPlPGwbTtw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/vue-router/-/vue-router-4.5.1.tgz}
    id: registry.npmjs.org/vue-router/4.5.1
    name: vue-router
    version: 4.5.1
    peerDependencies:
      vue: ^3.2.0
    dependencies:
      '@vue/devtools-api': registry.npmjs.org/@vue/devtools-api/6.6.4
      vue: registry.npmjs.org/vue/3.5.22_typescript@5.8.3
    dev: false

  registry.npmjs.org/vue-tsc/3.0.8_typescript@5.8.3:
    resolution: {integrity: sha512-H9yg/m6ywykmWS+pIAEs65v2FrVm5uOA0a0dHkX6Sx8dNg1a1m4iudt/6eGa9fAenmNHGlLFN9XpWQb8i5sU1w==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/vue-tsc/-/vue-tsc-3.0.8.tgz}
    id: registry.npmjs.org/vue-tsc/3.0.8
    name: vue-tsc
    version: 3.0.8
    hasBin: true
    peerDependencies:
      typescript: '>=5.0.0'
    dependencies:
      '@volar/typescript': registry.npmjs.org/@volar/typescript/2.4.23
      '@vue/language-core': registry.npmjs.org/@vue/language-core/3.0.8_typescript@5.8.3
      typescript: registry.npmjs.org/typescript/5.8.3
    dev: true

  registry.npmjs.org/vue-web-component-wrapper/1.7.7:
    resolution: {integrity: sha512-2uy6VdN8AwSzCeqc9tV4ZK2HKtgZ/NWL1rvdgOsddF1UFtszBZHKyQT9sDBUc4BpyXmP7f8tmI1rI0n/A6Qptw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/vue-web-component-wrapper/-/vue-web-component-wrapper-1.7.7.tgz}
    name: vue-web-component-wrapper
    version: 1.7.7
    dev: false

  registry.npmjs.org/vue/3.5.22_typescript@5.8.3:
    resolution: {integrity: sha512-toaZjQ3a/G/mYaLSbV+QsQhIdMo9x5rrqIpYRObsJ6T/J+RyCSFwN2LHNVH9v8uIcljDNa3QzPVdv3Y6b9hAJQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/vue/-/vue-3.5.22.tgz}
    id: registry.npmjs.org/vue/3.5.22
    name: vue
    version: 3.5.22
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@vue/compiler-dom': registry.npmjs.org/@vue/compiler-dom/3.5.22
      '@vue/compiler-sfc': registry.npmjs.org/@vue/compiler-sfc/3.5.22
      '@vue/runtime-dom': registry.npmjs.org/@vue/runtime-dom/3.5.22
      '@vue/server-renderer': registry.npmjs.org/@vue/server-renderer/3.5.22_vue@3.5.22
      '@vue/shared': registry.npmjs.org/@vue/shared/3.5.22
      typescript: registry.npmjs.org/typescript/5.8.3

  registry.npmjs.org/which/2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/which/-/which-2.0.2.tgz}
    name: which
    version: 2.0.2
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: registry.npmjs.org/isexe/2.0.0
    dev: true

  registry.npmjs.org/which/5.0.0:
    resolution: {integrity: sha512-JEdGzHwwkrbWoGOlIHqQ5gtprKGOenpDHpxE9zVR1bWbOtYRyPPHMe9FaP6x61CmNaTThSkb0DAJte5jD+DmzQ==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/which/-/which-5.0.0.tgz}
    name: which
    version: 5.0.0
    engines: {node: ^18.17.0 || >=20.5.0}
    hasBin: true
    dependencies:
      isexe: registry.npmjs.org/isexe/3.1.1
    dev: true

  registry.npmjs.org/word-wrap/1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz}
    name: word-wrap
    version: 1.2.5
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/wsl-utils/0.1.0:
    resolution: {integrity: sha512-h3Fbisa2nKGPxCpm89Hk33lBLsnaGBvctQopaBSOW/uIs6FTe1ATyAnKFJrzVs9vpGdsTe73WF3V4lIsk4Gacw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/wsl-utils/-/wsl-utils-0.1.0.tgz}
    name: wsl-utils
    version: 0.1.0
    engines: {node: '>=18'}
    dependencies:
      is-wsl: registry.npmjs.org/is-wsl/3.1.0
    dev: true

  registry.npmjs.org/xml-name-validator/4.0.0:
    resolution: {integrity: sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-4.0.0.tgz}
    name: xml-name-validator
    version: 4.0.0
    engines: {node: '>=12'}
    dev: true

  registry.npmjs.org/yallist/3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz}
    name: yallist
    version: 3.1.1
    dev: true

  registry.npmjs.org/yocto-queue/0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz}
    name: yocto-queue
    version: 0.1.0
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/yoctocolors/2.1.2:
    resolution: {integrity: sha512-CzhO+pFNo8ajLM2d2IW/R93ipy99LWjtwblvC1RsoSUMZgyLbYFr221TnSNT7GjGdYui6P459mw9JH/g/zW2ug==, registry: https://registry.yarnpkg.com/, tarball: https://registry.npmjs.org/yoctocolors/-/yoctocolors-2.1.2.tgz}
    name: yoctocolors
    version: 2.1.2
    engines: {node: '>=18'}
    dev: true
